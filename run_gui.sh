#!/bin/bash

# ARINC 424 GUI Launcher Script
# This script sets up the environment and launches the GUI application

echo "ARINC 424 Database GUI Parser"
echo "=============================="

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "arinc_gui.py" ]; then
    echo "Error: arinc_gui.py not found in current directory"
    echo "Please run this script from the arinc424 project directory"
    exit 1
fi

# Check if arinc424 library is installed
python3 -c "import arinc424" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "ARINC424 library not found. Installing..."
    python3 -m pip install -e . --user
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install arinc424 library"
        exit 1
    fi
    echo "Library installed successfully!"
fi

# Check if tkinter is available
python3 -c "import tkinter" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Error: tkinter is not available"
    echo "Please install tkinter: sudo apt-get install python3-tk (on Ubuntu/Debian)"
    exit 1
fi

echo "Starting GUI application..."
python3 arinc_gui.py

echo "GUI application closed."
