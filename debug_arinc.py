#!/usr/bin/env python3
"""
Debug script to identify string index out of range errors in ARINC file parsing.
"""

import sys
import traceback
import arinc424
from coordinate_utils import parse_arinc_coordinate, extract_coordinates_from_record

def debug_file_parsing(filepath, max_lines=50):
    """Debug ARINC file parsing line by line."""
    print(f"Debugging ARINC file: {filepath}")
    print("=" * 60)
    
    try:
        with open(filepath, 'r') as f:
            for line_num, line in enumerate(f, 1):
                if line_num > max_lines:
                    print(f"... stopping at line {max_lines} for debugging")
                    break
                
                line = line.strip()
                if not line or line.startswith('*') or line.startswith('#'):
                    print(f"Line {line_num}: SKIPPED (comment/empty)")
                    continue
                
                print(f"\nLine {line_num}: Length={len(line)}")
                print(f"Content: {line}")
                
                # Test basic record parsing
                try:
                    record = arinc424.Record()
                    if record.read(line):
                        print(f"✓ Record parsed successfully")
                        print(f"  Identifier: {record.ident}")
                        print(f"  Fields: {len(record.fields)}")
                        
                        # Test coordinate extraction
                        try:
                            record_data = {
                                'line_number': line_num,
                                'raw': line,
                                'record': record,
                                'category': record.ident,
                                'airport': 'TEST'
                            }
                            
                            coords = extract_coordinates_from_record(record_data)
                            if coords:
                                print(f"  Coordinates found: {len(coords)}")
                                for coord in coords:
                                    print(f"    {coord['name']}: {coord['lat']:.6f}, {coord['lon']:.6f}")
                            else:
                                print(f"  No coordinates found")
                                
                        except Exception as coord_error:
                            print(f"✗ Coordinate extraction error: {coord_error}")
                            print(f"  Traceback: {traceback.format_exc()}")
                            
                            # Debug coordinate fields
                            print(f"  Available fields:")
                            for field in record.fields:
                                if 'Latitude' in field.name or 'Longitude' in field.name:
                                    print(f"    {field.name}: '{field.value}'")
                        
                    else:
                        print(f"✗ Record parsing failed")
                        
                except Exception as record_error:
                    print(f"✗ Record parsing error: {record_error}")
                    print(f"  Traceback: {traceback.format_exc()}")
                    
                    # Analyze the line structure
                    print(f"  Line analysis:")
                    print(f"    Length: {len(line)}")
                    if len(line) >= 10:
                        print(f"    First 10 chars: '{line[:10]}'")
                        print(f"    Chars 4-6: '{line[4:6]}'")
                        print(f"    Chars 6-10: '{line[6:10]}'")
                    
                    return False
                    
    except Exception as file_error:
        print(f"File reading error: {file_error}")
        return False
    
    return True

def debug_coordinate_parsing():
    """Debug coordinate parsing with various formats."""
    print("\nDebugging Coordinate Parsing")
    print("=" * 40)
    
    # Test various coordinate formats that might cause issues
    test_coordinates = [
        ('N47265700', 'lat'),
        ('W122182910', 'lon'),
        ('N40471600', 'lat'),
        ('W073463900', 'lon'),
        ('', 'lat'),  # Empty string
        ('N', 'lat'),  # Too short
        ('N472657', 'lat'),  # Missing digits
        ('X47265700', 'lat'),  # Invalid hemisphere
        ('N472657001', 'lat'),  # Too long
    ]
    
    for coord_str, coord_type in test_coordinates:
        try:
            result = parse_arinc_coordinate(coord_str, coord_type)
            print(f"✓ '{coord_str}' ({coord_type}) -> {result}")
        except Exception as e:
            print(f"✗ '{coord_str}' ({coord_type}) -> ERROR: {e}")
            print(f"  Traceback: {traceback.format_exc()}")

def debug_specific_line(line_content):
    """Debug a specific line that's causing issues."""
    print(f"\nDebugging specific line:")
    print(f"Content: {line_content}")
    print(f"Length: {len(line_content)}")
    
    # Character by character analysis
    print("Character analysis:")
    for i, char in enumerate(line_content[:20]):  # First 20 characters
        print(f"  {i:2d}: '{char}' (ASCII {ord(char)})")
    
    # Try parsing
    try:
        record = arinc424.Record()
        if record.read(line_content):
            print("✓ Record parsed successfully")
            
            # Show all fields
            print("Fields:")
            for field in record.fields:
                print(f"  {field.name}: '{field.value}'")
                
        else:
            print("✗ Record parsing failed")
            
    except Exception as e:
        print(f"✗ Parsing error: {e}")
        print(f"Traceback: {traceback.format_exc()}")

def safe_get_airport_identifier(record):
    """Safely extract airport identifier to avoid index errors."""
    try:
        # Try to find airport identifier in the record fields
        for field in record.fields:
            if 'Airport' in field.name and 'Identifier' in field.name:
                return field.value.strip()
            elif 'Heliport' in field.name and 'Identifier' in field.name:
                return field.value.strip()
        
        # Fallback: extract from raw record based on position
        if hasattr(record, 'raw') and len(record.raw) >= 10:
            return record.raw[6:10].strip()
        
        return 'UNKNOWN'
        
    except Exception as e:
        print(f"Error extracting airport identifier: {e}")
        return 'ERROR'

def main():
    """Main debug function."""
    if len(sys.argv) < 2:
        print("Usage: python3 debug_arinc.py <arinc_file_path> [max_lines]")
        print("\nThis script will help debug string index out of range errors")
        print("by analyzing your ARINC file line by line.")
        return 1
    
    filepath = sys.argv[1]
    max_lines = int(sys.argv[2]) if len(sys.argv) > 2 else 10
    
    print("ARINC File Debug Tool")
    print("=" * 50)
    
    # First, debug coordinate parsing
    debug_coordinate_parsing()
    
    # Then debug file parsing
    if debug_file_parsing(filepath, max_lines):
        print("\n✓ File parsing completed without major errors")
    else:
        print("\n✗ File parsing encountered errors")
    
    print("\nIf you're still getting errors, please:")
    print("1. Share the specific error message")
    print("2. Share a sample line that's causing the issue")
    print("3. Run this script with a larger max_lines value")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
