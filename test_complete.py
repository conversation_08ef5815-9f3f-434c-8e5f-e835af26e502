#!/usr/bin/env python3
"""
Comprehensive test script for the enhanced ARINC GUI application with mapping.
"""

import os
import sys
from arinc_gui import ARINCParser, MAPPING_AVAILABLE, MATPLOTLIB_AVAILABLE
from coordinate_utils import parse_arinc_coordinate, extract_coordinates_from_record, calculate_bounds

def test_coordinate_parsing():
    """Test coordinate parsing functionality."""
    print("Testing Coordinate Parsing")
    print("-" * 30)
    
    test_cases = [
        ('N47265700', 'lat', 47.449167),  # Seattle airport
        ('W122182910', 'lon', -122.308083),
        ('N40471600', 'lat', 40.787778),  # JFK airport  
        ('W073463900', 'lon', -73.777500)
    ]
    
    all_passed = True
    for coord_str, coord_type, expected in test_cases:
        result = parse_arinc_coordinate(coord_str, coord_type)
        if result is not None:
            diff = abs(result - expected)
            if diff < 0.001:  # Allow small floating point differences
                print(f"✓ {coord_str} -> {result:.6f}")
            else:
                print(f"✗ {coord_str} -> {result:.6f} (expected {expected:.6f})")
                all_passed = False
        else:
            print(f"✗ {coord_str} -> None (expected {expected:.6f})")
            all_passed = False
    
    return all_passed

def test_parser_with_coordinates():
    """Test the enhanced parser with coordinate extraction."""
    print("\nTesting Enhanced Parser")
    print("-" * 30)
    
    parser = ARINCParser()
    
    # Test with different file types
    test_files = [
        ('data/ARINC-424-18/airport', 'Airport data'),
        ('data/ARINC-424-18/runway', 'Runway data'),
        ('data/ARINC-424-18/terminal_waypoint', 'Terminal waypoint data'),
        ('data/ARINC-424-18/navaid_vhf', 'VHF navaid data')
    ]
    
    total_records = 0
    total_coordinates = 0
    
    for filepath, description in test_files:
        if os.path.exists(filepath):
            try:
                parser.parse_file(filepath)
                records = len(parser.raw_records)
                coords = len(parser.coordinates)
                
                print(f"✓ {description}: {records} records, {coords} coordinates")
                total_records += records
                total_coordinates += coords
                
                # Show sample coordinates
                if coords > 0:
                    sample_coord = parser.coordinates[0]
                    print(f"    Sample: {sample_coord['name']} at {sample_coord['lat']:.6f}, {sample_coord['lon']:.6f}")
                
            except Exception as e:
                print(f"✗ Error parsing {filepath}: {e}")
                return False
        else:
            print(f"⚠ File not found: {filepath}")
    
    print(f"\nTotal: {total_records} records, {total_coordinates} coordinates")
    return total_coordinates > 0

def test_coordinate_filtering():
    """Test coordinate filtering by category and airport."""
    print("\nTesting Coordinate Filtering")
    print("-" * 30)
    
    parser = ARINCParser()
    
    # Parse multiple files to get diverse data
    for filename in ['airport', 'runway', 'terminal_waypoint']:
        filepath = f'data/ARINC-424-18/{filename}'
        if os.path.exists(filepath):
            parser.parse_file(filepath)
    
    if not parser.coordinates:
        print("✗ No coordinates to test filtering")
        return False
    
    # Test filtering by category
    categories = set(coord['category'] for coord in parser.coordinates)
    print(f"Categories found: {list(categories)}")
    
    for category in categories:
        filtered = parser.get_coordinates(category=category)
        print(f"  {category}: {len(filtered)} coordinates")
    
    # Test filtering by airport
    airports = set(coord['airport'] for coord in parser.coordinates)
    print(f"Airports found: {list(airports)}")
    
    for airport in list(airports)[:3]:  # Test first 3 airports
        filtered = parser.get_coordinates(airport=airport)
        print(f"  {airport}: {len(filtered)} coordinates")
    
    return True

def test_bounds_calculation():
    """Test coordinate bounds calculation."""
    print("\nTesting Bounds Calculation")
    print("-" * 30)
    
    parser = ARINCParser()
    parser.parse_file('data/ARINC-424-18/terminal_waypoint')
    
    if not parser.coordinates:
        print("✗ No coordinates for bounds testing")
        return False
    
    bounds = calculate_bounds(parser.coordinates)
    if bounds:
        print(f"✓ Bounds calculated:")
        print(f"    Center: {bounds['center_lat']:.6f}, {bounds['center_lon']:.6f}")
        print(f"    Lat range: {bounds['min_lat']:.6f} to {bounds['max_lat']:.6f}")
        print(f"    Lon range: {bounds['min_lon']:.6f} to {bounds['max_lon']:.6f}")
        return True
    else:
        print("✗ Failed to calculate bounds")
        return False

def test_map_generation():
    """Test map generation if folium is available."""
    print("\nTesting Map Generation")
    print("-" * 30)
    
    if not MAPPING_AVAILABLE:
        print("⚠ Folium not available - skipping map test")
        return True
    
    try:
        import folium
        
        parser = ARINCParser()
        parser.parse_file('data/ARINC-424-18/airport')
        
        if not parser.coordinates:
            print("✗ No coordinates for map testing")
            return False
        
        # Create simple test map
        coord = parser.coordinates[0]
        m = folium.Map(location=[coord['lat'], coord['lon']], zoom_start=10)
        
        folium.Marker(
            [coord['lat'], coord['lon']],
            popup=coord['name'],
            tooltip=coord['name']
        ).add_to(m)
        
        # Save test map
        test_map_file = 'test_complete_map.html'
        m.save(test_map_file)
        
        print(f"✓ Test map created: {test_map_file}")
        return True
        
    except Exception as e:
        print(f"✗ Map generation failed: {e}")
        return False

def test_gui_components():
    """Test GUI component availability."""
    print("\nTesting GUI Components")
    print("-" * 30)
    
    try:
        import tkinter as tk
        
        # Test basic tkinter
        root = tk.Tk()
        root.withdraw()  # Hide window
        print("✓ Tkinter available")
        root.destroy()
        
        # Test GUI class import
        from arinc_gui import ARINCGUIApp
        print("✓ GUI class imports successfully")
        
        # Test mapping availability
        if MAPPING_AVAILABLE:
            print("✓ Mapping features available")
        else:
            print("⚠ Mapping features not available (folium missing)")
        
        if MATPLOTLIB_AVAILABLE:
            print("✓ Matplotlib available")
        else:
            print("⚠ Matplotlib not available")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI component test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("ARINC GUI Complete Test Suite")
    print("=" * 50)
    
    tests = [
        ("Coordinate Parsing", test_coordinate_parsing),
        ("Enhanced Parser", test_parser_with_coordinates),
        ("Coordinate Filtering", test_coordinate_filtering),
        ("Bounds Calculation", test_bounds_calculation),
        ("Map Generation", test_map_generation),
        ("GUI Components", test_gui_components)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✓ {test_name} PASSED")
                passed += 1
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The enhanced GUI is ready to use.")
        print("\nTo run the GUI application:")
        print("  python3 arinc_gui.py")
        print("\nFeatures available:")
        print("  • Parse ARINC 424 files")
        print("  • Organize by categories and airports")
        print("  • View detailed record information")
        print("  • Export data in multiple formats")
        if MAPPING_AVAILABLE:
            print("  • Interactive map visualization")
        print("  • Coordinate display and analysis")
    else:
        print(f"⚠ {total - passed} tests failed. Check the output above for details.")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
