#!/usr/bin/env python3
"""
Demo script showing how to use the ARINC GUI application programmatically.
This demonstrates the parser functionality without the GUI.
"""

import os
from arinc_gui import ARINCParser

def demo_parser():
    """Demonstrate the ARINC parser functionality."""
    print("ARINC 424 Database Parser Demo")
    print("=" * 50)
    
    parser = ARINCParser()
    
    # Demo with different file types
    demo_files = [
        ('data/ARINC-424-18/airport', 'Airport Data'),
        ('data/ARINC-424-18/runway', 'Runway Data'),
        ('data/ARINC-424-18/navaid_vhf', 'VHF Navaid Data'),
        ('data/ARINC-424-18/terminal_waypoint', 'Terminal Waypoint Data')
    ]
    
    for filepath, description in demo_files:
        if os.path.exists(filepath):
            print(f"\n{description}")
            print("-" * len(description))
            
            try:
                parser.parse_file(filepath)
                
                print(f"Total records: {len(parser.raw_records)}")
                print(f"Categories: {len(parser.get_categories())}")
                
                # Show category breakdown
                for category in parser.get_categories():
                    category_name = parser.get_category_name(category)
                    airports = parser.get_airports_in_category(category)
                    
                    print(f"\nCategory: {category} - {category_name}")
                    print(f"  Airports: {len(airports)}")
                    
                    # Show airport details
                    for airport in airports[:3]:  # Show first 3 airports
                        records = parser.get_records_for_airport(category, airport)
                        print(f"    {airport}: {len(records)} records")
                        
                        # Show sample record details
                        if records:
                            sample_record = records[0]['record']
                            print(f"      Sample fields: {len(sample_record.fields)}")
                            
                            # Show first few fields
                            for field in sample_record.fields[:3]:
                                decoded = field.decode(sample_record)
                                print(f"        {field.name}: '{field.value}' -> {decoded}")
                    
                    if len(airports) > 3:
                        print(f"    ... and {len(airports) - 3} more airports")
                
            except Exception as e:
                print(f"Error parsing {filepath}: {e}")
        else:
            print(f"\nFile not found: {filepath}")
    
    print("\n" + "=" * 50)
    print("Demo completed!")
    print("\nTo run the full GUI application:")
    print("  python3 arinc_gui.py")
    print("\nOr use the launcher script:")
    print("  ./run_gui.sh")

def demo_export():
    """Demonstrate export functionality."""
    print("\nExport Demo")
    print("-" * 20)
    
    parser = ARINCParser()
    
    # Parse a sample file
    sample_file = 'data/ARINC-424-18/airport'
    if os.path.exists(sample_file):
        parser.parse_file(sample_file)
        
        # Export to JSON
        import json
        export_data = []
        
        for record_data in parser.raw_records:
            record = record_data['record']
            record_dict = {
                'line_number': record_data['line_number'],
                'category': record_data['category'],
                'airport': record_data['airport'],
                'raw': record_data['raw'],
                'fields': {}
            }
            
            for field in record.fields:
                record_dict['fields'][field.name] = {
                    'value': field.value,
                    'decoded': field.decode(record)
                }
            
            export_data.append(record_dict)
        
        # Save to demo file
        with open('demo_export.json', 'w') as f:
            json.dump(export_data, f, indent=2)
        
        print(f"Exported {len(export_data)} records to demo_export.json")
        
        # Show sample of exported data
        if export_data:
            print("\nSample exported record:")
            sample = export_data[0]
            print(f"  Line: {sample['line_number']}")
            print(f"  Category: {sample['category']}")
            print(f"  Airport: {sample['airport']}")
            print(f"  Fields: {len(sample['fields'])}")
            
            # Show first few fields
            for i, (field_name, field_data) in enumerate(list(sample['fields'].items())[:3]):
                print(f"    {field_name}: '{field_data['value']}' -> {field_data['decoded']}")
    
    else:
        print("Sample file not found for export demo")

if __name__ == "__main__":
    demo_parser()
    demo_export()
