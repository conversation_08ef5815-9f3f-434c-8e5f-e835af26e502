#!/usr/bin/env python3
"""
Simple script to test parsing of custom ARINC files.
Usage: python3 test_custom_file.py <filepath> [max_lines]
"""

import sys
import os
import arinc424
import traceback

def test_arinc_file(filepath, max_lines=10):
    """Test parsing an ARINC file."""
    print(f"Testing ARINC file: {filepath}")
    print("=" * 50)
    
    if not os.path.exists(filepath):
        print(f"Error: File not found: {filepath}")
        return False
    
    try:
        with open(filepath, 'r') as f:
            lines = f.readlines()
        
        print(f"File contains {len(lines)} lines")
        print(f"Testing first {min(max_lines, len(lines))} lines...")
        print()
        
        success_count = 0
        error_count = 0
        
        for i, line in enumerate(lines[:max_lines]):
            line_num = i + 1
            line = line.strip()
            
            print(f"Line {line_num}: Length={len(line)}")
            
            if not line:
                print("  → Empty line, skipping")
                continue
            
            if line.startswith('*') or line.startswith('#'):
                print("  → Comment line, skipping")
                continue

            if line.startswith('HDR'):
                print("  → Header line, skipping")
                continue
            
            # Show first part of line
            preview = line[:50] + "..." if len(line) > 50 else line
            print(f"  Content: {preview}")
            
            try:
                record = arinc424.Record()
                if record.read(line):
                    print(f"  ✓ SUCCESS: Parsed as {record.ident} with {len(record.fields)} fields")
                    success_count += 1
                    
                    # Show some key fields
                    for field in record.fields[:3]:
                        print(f"    {field.name}: '{field.value}'")
                else:
                    print(f"  ✗ FAILED: Could not parse record")
                    error_count += 1
                    
            except Exception as e:
                print(f"  ✗ ERROR: {e}")
                error_count += 1
                if "index" in str(e).lower():
                    print(f"    This looks like a string index error")
                    print(f"    Line length: {len(line)}")
                    if len(line) < 132:
                        print(f"    WARNING: ARINC lines should be 132 characters, this is only {len(line)}")
            
            print()
        
        print("=" * 50)
        print(f"SUMMARY:")
        print(f"  Successful parses: {success_count}")
        print(f"  Errors: {error_count}")
        print(f"  Total tested: {success_count + error_count}")
        
        if error_count == 0:
            print("  🎉 All lines parsed successfully!")
        else:
            print(f"  ⚠️  {error_count} lines had errors")
        
        return error_count == 0
        
    except Exception as e:
        print(f"File reading error: {e}")
        traceback.print_exc()
        return False

def test_with_gui_parser(filepath):
    """Test with the enhanced GUI parser."""
    print(f"\nTesting with enhanced GUI parser...")
    print("-" * 30)
    
    try:
        from arinc_gui import ARINCParser
        
        parser = ARINCParser()
        parser.parse_file(filepath)
        
        print(f"✓ Successfully parsed {len(parser.raw_records)} records")
        print(f"✓ Found {len(parser.coordinates)} coordinates")
        print(f"✓ Categories: {parser.get_categories()}")
        
        # Show coordinate summary
        if parser.coordinates:
            print("\nCoordinate summary:")
            for coord in parser.coordinates[:3]:
                print(f"  {coord['name']}: {coord['lat']:.6f}, {coord['lon']:.6f}")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI parser error: {e}")
        traceback.print_exc()
        return False

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 test_custom_file.py <filepath> [max_lines]")
        print("\nExample:")
        print("  python3 test_custom_file.py ../flyright.pc 10")
        return 1
    
    filepath = sys.argv[1]
    max_lines = int(sys.argv[2]) if len(sys.argv) > 2 else 10
    
    print("ARINC Custom File Tester")
    print("=" * 40)
    
    # Test basic parsing
    basic_success = test_arinc_file(filepath, max_lines)
    
    # Test with GUI parser if basic parsing worked
    if basic_success:
        gui_success = test_with_gui_parser(filepath)
        if gui_success:
            print("\n🎉 Your file should work with the GUI application!")
            print("Run: python3 arinc_gui.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
