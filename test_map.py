#!/usr/bin/env python3
"""
Test script for the map functionality of the ARINC GUI application.
"""

import os
from arinc_gui import ARINCParser
from coordinate_utils import calculate_bounds, format_coordinate_display

def test_map_generation():
    """Test map generation with sample data."""
    print("Testing Map Generation")
    print("=" * 30)
    
    try:
        import folium
        print("✓ Folium available")
    except ImportError:
        print("✗ Folium not available - installing...")
        os.system("python3 -m pip install folium --user")
        try:
            import folium
            print("✓ Folium installed successfully")
        except ImportError:
            print("✗ Failed to install folium")
            return False
    
    # Parse sample data
    parser = ARINCParser()
    
    # Test with multiple files to get more coordinates
    test_files = ['airport', 'runway', 'terminal_waypoint']
    all_coordinates = []
    
    for filename in test_files:
        filepath = f'data/ARINC-424-18/{filename}'
        if os.path.exists(filepath):
            parser.parse_file(filepath)
            coords = parser.get_coordinates()
            all_coordinates.extend(coords)
            print(f"Loaded {len(coords)} coordinates from {filename}")
    
    if not all_coordinates:
        print("No coordinates found to plot")
        return False
    
    print(f"\nTotal coordinates to plot: {len(all_coordinates)}")
    
    # Calculate bounds
    bounds = calculate_bounds(all_coordinates)
    if bounds:
        print(f"Map bounds:")
        print(f"  Center: {bounds['center_lat']:.6f}, {bounds['center_lon']:.6f}")
        print(f"  Lat range: {bounds['min_lat']:.6f} to {bounds['max_lat']:.6f}")
        print(f"  Lon range: {bounds['min_lon']:.6f} to {bounds['max_lon']:.6f}")
    
    # Create test map
    center_lat = bounds['center_lat']
    center_lon = bounds['center_lon']
    
    m = folium.Map(location=[center_lat, center_lon], zoom_start=10)
    
    # Color mapping for different coordinate types
    color_map = {
        'Airport': 'red',
        'Runway': 'blue', 
        'Waypoint': 'green',
        'Localizer': 'purple',
        'Glideslope': 'orange',
        'Marker': 'pink',
        'Navaid': 'darkgreen',
        'Other': 'gray'
    }
    
    # Add markers
    for coord in all_coordinates:
        color = color_map.get(coord['type'], 'gray')
        
        popup_text = f"""
        <b>{coord['name']}</b><br>
        Type: {coord['type']}<br>
        Category: {coord['category']}<br>
        Airport: {coord['airport']}<br>
        Coordinates: {format_coordinate_display(coord['lat'], coord['lon'])}<br>
        Lat: {coord['lat']:.6f}<br>
        Lon: {coord['lon']:.6f}
        """
        
        folium.Marker(
            [coord['lat'], coord['lon']],
            popup=folium.Popup(popup_text, max_width=300),
            tooltip=coord['name'],
            icon=folium.Icon(color=color, icon='info-sign')
        ).add_to(m)
    
    # Save test map
    map_file = 'test_map.html'
    m.save(map_file)
    print(f"\n✓ Test map saved as {map_file}")
    print(f"  Open in browser: file://{os.path.abspath(map_file)}")
    
    # Show coordinate summary
    print(f"\nCoordinate Summary:")
    type_counts = {}
    for coord in all_coordinates:
        coord_type = coord['type']
        type_counts[coord_type] = type_counts.get(coord_type, 0) + 1
    
    for coord_type, count in sorted(type_counts.items()):
        print(f"  {coord_type}: {count}")
    
    return True

def test_coordinate_display():
    """Test coordinate display functionality."""
    print("\n" + "=" * 50)
    print("Testing Coordinate Display")
    print("=" * 30)
    
    parser = ARINCParser()
    parser.parse_file('data/ARINC-424-18/airport')
    
    coordinates = parser.get_coordinates()
    
    if coordinates:
        print(f"Found {len(coordinates)} coordinates:")
        for coord in coordinates:
            formatted = format_coordinate_display(coord['lat'], coord['lon'])
            print(f"  {coord['name']}")
            print(f"    Type: {coord['type']}")
            print(f"    Decimal: {coord['lat']:.6f}, {coord['lon']:.6f}")
            print(f"    Formatted: {formatted}")
            print(f"    Raw: {coord['raw_lat']}, {coord['raw_lon']}")
    else:
        print("No coordinates found")

def main():
    """Main test function."""
    print("ARINC GUI Map Testing")
    print("=" * 40)
    
    # Test map generation
    if test_map_generation():
        print("\n✓ Map generation test passed")
    else:
        print("\n✗ Map generation test failed")
    
    # Test coordinate display
    test_coordinate_display()
    
    print("\n" + "=" * 50)
    print("Map testing completed!")
    print("\nTo test the full GUI with maps:")
    print("  python3 arinc_gui.py")
    print("  Then use View -> Show Map or the Map button")

if __name__ == "__main__":
    main()
