#!/usr/bin/env python3
"""
Coordinate utilities for parsing ARINC 424 geographic coordinates.
"""

import re
import math


def parse_arinc_coordinate(coord_str, coord_type='lat'):
    """
    Parse ARINC 424 coordinate format to decimal degrees.
    
    ARINC format examples:
    - Latitude: N47265700 = N 47° 26' 57.00"
    - Longitude: W122182910 = W 122° 18' 29.10"
    
    Args:
        coord_str: ARINC coordinate string
        coord_type: 'lat' for latitude, 'lon' for longitude
        
    Returns:
        float: Decimal degrees (positive for N/E, negative for S/W)
    """
    if not coord_str or len(coord_str) < 8:
        return None
    
    coord_str = coord_str.strip()
    
    # Extract hemisphere
    hemisphere = coord_str[0]
    if hemisphere not in ['N', 'S', 'E', 'W']:
        return None
    
    # Extract numeric part
    numeric_part = coord_str[1:]
    
    try:
        if coord_type == 'lat':
            # Latitude format: DDMMSSSS (degrees, minutes, seconds*100)
            if len(numeric_part) >= 8:
                degrees = int(numeric_part[:2])
                minutes = int(numeric_part[2:4])
                seconds = int(numeric_part[4:8]) / 100.0
            else:
                return None
        else:  # longitude
            # Longitude format: DDDMMSSSS (degrees, minutes, seconds*100)
            if len(numeric_part) >= 9:
                degrees = int(numeric_part[:3])
                minutes = int(numeric_part[3:5])
                seconds = int(numeric_part[5:9]) / 100.0
            else:
                return None
        
        # Convert to decimal degrees
        decimal_degrees = degrees + minutes/60.0 + seconds/3600.0
        
        # Apply hemisphere
        if hemisphere in ['S', 'W']:
            decimal_degrees = -decimal_degrees
            
        return decimal_degrees
        
    except (ValueError, IndexError):
        return None


def extract_coordinates_from_record(record_data):
    """
    Extract all coordinate pairs from an ARINC record.
    
    Args:
        record_data: Dictionary containing parsed record information
        
    Returns:
        list: List of coordinate dictionaries with lat, lon, name, type
    """
    coordinates = []
    record = record_data['record']
    
    # Common coordinate field patterns
    coordinate_patterns = [
        ('Latitude', 'Longitude'),
        ('Airport Reference Pt. Latitude', 'Airport Reference Pt. Longitude'),
        ('Waypoint Latitude', 'Waypoint Longitude'),
        ('Runway Latitude', 'Runway Longitude'),
        ('Localizer Latitude', 'Localizer Longitude'),
        ('Glide Slope Latitude', 'Glide Slope Longitude'),
        ('Azimuth Latitude', 'Azimuth Longitude'),
        ('Elevation Latitude', 'Elevation Longitude'),
        ('Marker Latitude', 'Marker Longitude'),
        ('Locator Latitude', 'Locator Longitude'),
        ('Back Azimuth Latitude', 'Back Azimuth Longitude'),
        ('MLS Datum Point Latitude', 'MLS Datum Point Longitude')
    ]
    
    # Create field lookup
    field_dict = {}
    for field in record.fields:
        field_dict[field.name] = field.value
    
    # Extract coordinate pairs
    for lat_field, lon_field in coordinate_patterns:
        if lat_field in field_dict and lon_field in field_dict:
            lat_str = field_dict[lat_field]
            lon_str = field_dict[lon_field]
            
            lat = parse_arinc_coordinate(lat_str, 'lat')
            lon = parse_arinc_coordinate(lon_str, 'lon')
            
            if lat is not None and lon is not None:
                # Determine coordinate type and name
                coord_type = determine_coordinate_type(lat_field)
                coord_name = get_coordinate_name(record_data, lat_field)
                
                coordinates.append({
                    'lat': lat,
                    'lon': lon,
                    'name': coord_name,
                    'type': coord_type,
                    'category': record_data['category'],
                    'airport': record_data['airport'],
                    'raw_lat': lat_str,
                    'raw_lon': lon_str,
                    'field_name': lat_field.replace(' Latitude', '')
                })
    
    return coordinates


def determine_coordinate_type(field_name):
    """Determine the type of coordinate based on field name."""
    field_lower = field_name.lower()
    
    if 'airport' in field_lower or 'reference' in field_lower:
        return 'Airport'
    elif 'runway' in field_lower:
        return 'Runway'
    elif 'waypoint' in field_lower:
        return 'Waypoint'
    elif 'localizer' in field_lower:
        return 'Localizer'
    elif 'glide' in field_lower or 'glideslope' in field_lower:
        return 'Glideslope'
    elif 'marker' in field_lower:
        return 'Marker'
    elif 'navaid' in field_lower:
        return 'Navaid'
    elif 'azimuth' in field_lower:
        return 'Azimuth'
    elif 'elevation' in field_lower:
        return 'Elevation'
    else:
        return 'Other'


def get_coordinate_name(record_data, field_name):
    """Get a descriptive name for the coordinate."""
    record = record_data['record']
    airport = record_data['airport']
    
    # Try to find a name field
    name_fields = ['Airport Name', 'Waypoint Name / Desc', 'Waypoint Name/Description', 
                   'Name', 'Localizer Identifier', 'Runway Identifier']
    
    for field in record.fields:
        if field.name in name_fields and field.value.strip():
            name = field.value.strip()
            coord_type = determine_coordinate_type(field_name)
            return f"{airport} - {name} ({coord_type})"
    
    # Fallback to airport and coordinate type
    coord_type = determine_coordinate_type(field_name)
    return f"{airport} ({coord_type})"


def calculate_bounds(coordinates):
    """Calculate bounding box for a list of coordinates."""
    if not coordinates:
        return None
    
    lats = [coord['lat'] for coord in coordinates]
    lons = [coord['lon'] for coord in coordinates]
    
    return {
        'min_lat': min(lats),
        'max_lat': max(lats),
        'min_lon': min(lons),
        'max_lon': max(lons),
        'center_lat': sum(lats) / len(lats),
        'center_lon': sum(lons) / len(lons)
    }


def format_coordinate_display(lat, lon):
    """Format coordinates for display."""
    def dd_to_dms(dd, is_longitude=False):
        """Convert decimal degrees to degrees, minutes, seconds."""
        abs_dd = abs(dd)
        degrees = int(abs_dd)
        minutes = int((abs_dd - degrees) * 60)
        seconds = ((abs_dd - degrees) * 60 - minutes) * 60
        
        if is_longitude:
            hemisphere = 'E' if dd >= 0 else 'W'
        else:
            hemisphere = 'N' if dd >= 0 else 'S'
        
        return f"{degrees:02d}°{minutes:02d}'{seconds:05.2f}\"{hemisphere}"
    
    lat_dms = dd_to_dms(lat, False)
    lon_dms = dd_to_dms(lon, True)
    
    return f"{lat_dms}, {lon_dms}"


def test_coordinate_parsing():
    """Test coordinate parsing with sample data."""
    test_cases = [
        ('N47265700', 'lat', 47.449167),  # Seattle airport
        ('W122182910', 'lon', -122.308056),
        ('N40471600', 'lat', 40.787778),  # JFK airport
        ('W073463900', 'lon', -73.775833)
    ]
    
    print("Testing coordinate parsing:")
    for coord_str, coord_type, expected in test_cases:
        result = parse_arinc_coordinate(coord_str, coord_type)
        print(f"{coord_str} ({coord_type}) -> {result:.6f} (expected: {expected:.6f})")
        if result is not None:
            formatted = format_coordinate_display(result, 0 if coord_type == 'lat' else result)
            print(f"  Formatted: {formatted}")


if __name__ == "__main__":
    test_coordinate_parsing()
