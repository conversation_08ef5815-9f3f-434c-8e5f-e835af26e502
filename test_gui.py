#!/usr/bin/env python3
"""
Test script for the ARINC GUI application.
Tests the parser functionality with sample data.
"""

import os
import sys
from arinc_gui import ARINCParser

def test_parser():
    """Test the ARINC parser with sample data."""
    print("Testing ARINC Parser...")

    parser = ARINCParser()

    # Test with sample airport data
    sample_file = os.path.join('data', 'ARINC-424-18', 'airport')

    if not os.path.exists(sample_file):
        print(f"Sample file not found: {sample_file}")
        return False

    try:
        print(f"Parsing file: {sample_file}")
        parser.parse_file(sample_file)

        print(f"Total records parsed: {len(parser.raw_records)}")
        print(f"Categories found: {len(parser.get_categories())}")

        for category in parser.get_categories():
            airports = parser.get_airports_in_category(category)
            category_name = parser.get_category_name(category)
            print(f"\nCategory: {category} ({category_name})")
            print(f"  Airports: {len(airports)}")

            for airport in airports:
                records = parser.get_records_for_airport(category, airport)
                print(f"    {airport}: {len(records)} records")

                # Show details of first record
                if records:
                    record = records[0]['record']
                    print(f"      Sample record fields: {len(record.fields)}")
                    for field in record.fields[:3]:  # Show first 3 fields
                        print(f"        {field.name}: '{field.value}' -> {field.decode(record)}")

        return True

    except Exception as e:
        print(f"Error testing parser: {e}")
        return False

def test_multiple_files():
    """Test parser with multiple file types."""
    print("\n" + "="*60)
    print("Testing multiple file types...")

    parser = ARINCParser()
    data_dir = os.path.join('data', 'ARINC-424-18')

    if not os.path.exists(data_dir):
        print(f"Data directory not found: {data_dir}")
        return False

    test_files = [
        'airport',
        'runway',
        'navaid_vhf',
        'terminal_waypoint'
    ]

    for filename in test_files:
        filepath = os.path.join(data_dir, filename)
        if os.path.exists(filepath):
            try:
                print(f"\nTesting {filename}...")
                parser.parse_file(filepath)

                categories = parser.get_categories()
                total_records = len(parser.raw_records)

                print(f"  Records: {total_records}")
                print(f"  Categories: {categories}")

                # Show sample data
                for category in categories[:2]:  # Show first 2 categories
                    airports = parser.get_airports_in_category(category)
                    print(f"    {category}: {len(airports)} airports")

            except Exception as e:
                print(f"  Error parsing {filename}: {e}")
        else:
            print(f"  File not found: {filepath}")

    return True

def main():
    """Main test function."""
    print("ARINC GUI Application Test")
    print("=" * 40)

    # Test basic parser functionality
    if test_parser():
        print("\n✓ Basic parser test passed")
    else:
        print("\n✗ Basic parser test failed")
        return 1

    # Test multiple file types
    if test_multiple_files():
        print("\n✓ Multiple file test passed")
    else:
        print("\n✗ Multiple file test failed")
        return 1

    print("\n" + "="*60)
    print("All tests completed successfully!")
    print("\nTo run the GUI application:")
    print("  python3 arinc_gui.py")

    return 0

if __name__ == "__main__":
    sys.exit(main())