#!/usr/bin/env python3
"""
ARINC 424 Database GUI Parser

A GUI application for parsing and organizing ARINC 424 navigation database files.
Records are categorized by type and organized by airport for easy browsing.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import arinc424
import os
from collections import defaultdict
import json
import webbrowser
import tempfile
from coordinate_utils import extract_coordinates_from_record, calculate_bounds, format_coordinate_display

# Try to import mapping libraries
try:
    import folium
    MAPPING_AVAILABLE = True
except ImportError:
    MAPPING_AVAILABLE = False
    print("Warning: folium not available. Map features will be disabled.")

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("Warning: matplotlib not available. Plot features will be disabled.")


class ARINCParser:
    """Handles parsing ARINC files and organizing data by categories and airports."""
    
    def __init__(self):
        self.records = defaultdict(lambda: defaultdict(list))  # category -> airport -> records
        self.raw_records = []
        self.coordinates = []  # List of all extracted coordinates
        self.category_names = {
            'PA': 'Airport Reference Point',
            'PG': 'Runway',
            'PC': 'Terminal Waypoint', 
            'PD': 'SID/STAR/Approach',
            'PE': 'SID/STAR/Approach',
            'PF': 'SID/STAR/Approach',
            'PI': 'Localizer/ILS',
            'PL': 'MLS',
            'PM': 'Localizer Marker',
            'PN': 'NDB Navaid',
            'PS': 'MSA',
            'PT': 'GLS',
            'PV': 'Airport Communication',
            'D ': 'VHF Navaid',
            'DB': 'NDB Navaid',
            'EA': 'Enroute Waypoint',
            'EM': 'Airways Marker',
            'EP': 'Holding Pattern',
            'ER': 'Enroute Airways',
            'ET': 'Preferred Route',
            'EU': 'Enroute Airways Restriction',
            'EV': 'Enroute Communications',
            'HA': 'Heliport',
            'HC': 'Heliport Terminal Waypoint',
            'HD': 'Heliport SID/STAR/Approach',
            'HE': 'Heliport SID/STAR/Approach',
            'HF': 'Heliport SID/STAR/Approach',
            'HK': 'Heliport TAA',
            'HS': 'Heliport MSA',
            'HV': 'Heliport Communications',
            'AS': 'Grid MORA',
            'UC': 'Controlled Airspace',
            'UF': 'FIR/UIR',
            'UR': 'Restrictive Airspace'
        }
    
    def parse_file(self, filepath):
        """Parse an ARINC file and organize records by category and airport."""
        self.records.clear()
        self.raw_records.clear()
        self.coordinates.clear()

        try:
            with open(filepath, 'r') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('*') or line.startswith('#'):
                        continue

                    record = arinc424.Record()
                    if record.read(line):
                        # Get category (section code)
                        category = self._get_category(record)

                        # Get airport identifier
                        airport = self._get_airport_identifier(record)

                        # Store the record
                        record_data = {
                            'line_number': line_num,
                            'raw': line,
                            'record': record,
                            'category': category,
                            'airport': airport
                        }

                        self.records[category][airport].append(record_data)
                        self.raw_records.append(record_data)

                        # Extract coordinates from this record
                        coords = extract_coordinates_from_record(record_data)
                        self.coordinates.extend(coords)

        except Exception as e:
            raise Exception(f"Error parsing file: {str(e)}")
    
    def _get_category(self, record):
        """Extract category from record identifier."""
        return record.ident
    
    def _get_airport_identifier(self, record):
        """Extract airport identifier from record."""
        # Try to find airport identifier in the record fields
        for field in record.fields:
            if 'Airport' in field.name and 'Identifier' in field.name:
                return field.value.strip()
            elif 'Heliport' in field.name and 'Identifier' in field.name:
                return field.value.strip()
        
        # Fallback: extract from raw record based on position
        if len(record.raw) >= 10:
            return record.raw[6:10].strip()
        
        return 'UNKNOWN'
    
    def get_categories(self):
        """Get all available categories."""
        return sorted(self.records.keys())
    
    def get_airports_in_category(self, category):
        """Get all airports in a specific category."""
        if category in self.records:
            return sorted(self.records[category].keys())
        return []
    
    def get_records_for_airport(self, category, airport):
        """Get all records for a specific airport in a category."""
        if category in self.records and airport in self.records[category]:
            return self.records[category][airport]
        return []
    
    def get_category_name(self, category):
        """Get human-readable name for category."""
        return self.category_names.get(category, f"Unknown ({category})")

    def get_coordinates(self, category=None, airport=None):
        """Get coordinates, optionally filtered by category and/or airport."""
        if not category and not airport:
            return self.coordinates

        filtered_coords = []
        for coord in self.coordinates:
            if category and coord['category'] != category:
                continue
            if airport and coord['airport'] != airport:
                continue
            filtered_coords.append(coord)

        return filtered_coords

    def get_coordinate_bounds(self, category=None, airport=None):
        """Get bounding box for coordinates."""
        coords = self.get_coordinates(category, airport)
        return calculate_bounds(coords) if coords else None


class ARINCGUIApp:
    """Main GUI application for ARINC database parsing."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("ARINC 424 Database Parser")
        self.root.geometry("1200x800")
        
        self.parser = ARINCParser()
        self.current_file = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface."""
        # Create menu bar
        self.create_menu()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create main content area with three panels
        self.create_main_panels()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_menu(self):
        """Create the menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open ARINC File...", command=self.open_file, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="Export Selected...", command=self.export_selected)
        file_menu.add_command(label="Export All...", command=self.export_all)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Refresh", command=self.refresh_view)
        view_menu.add_command(label="Expand All", command=self.expand_all)
        view_menu.add_command(label="Collapse All", command=self.collapse_all)
        view_menu.add_separator()
        if MAPPING_AVAILABLE:
            view_menu.add_command(label="Show Map", command=self.show_map)
        view_menu.add_command(label="Show Coordinates", command=self.show_coordinates)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        
        # Bind keyboard shortcuts
        self.root.bind('<Control-o>', lambda e: self.open_file())
    
    def create_toolbar(self):
        """Create the toolbar."""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        ttk.Button(toolbar, text="Open File", command=self.open_file).pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        ttk.Button(toolbar, text="Refresh", command=self.refresh_view).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Export", command=self.export_selected).pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        if MAPPING_AVAILABLE:
            ttk.Button(toolbar, text="Show Map", command=self.show_map).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Coordinates", command=self.show_coordinates).pack(side=tk.LEFT, padx=2)
    
    def create_main_panels(self):
        """Create the main three-panel layout."""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create paned window for resizable panels
        paned = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # Left panel: Category tree
        self.create_category_panel(paned)
        
        # Middle panel: Airport list
        self.create_airport_panel(paned)
        
        # Right panel: Record details
        self.create_details_panel(paned)
    
    def create_category_panel(self, parent):
        """Create the category tree panel."""
        frame = ttk.LabelFrame(parent, text="Categories", padding=5)
        parent.add(frame, weight=1)
        
        # Tree view for categories
        self.category_tree = ttk.Treeview(frame, selectmode='browse')
        self.category_tree.pack(fill=tk.BOTH, expand=True)
        
        # Scrollbar for category tree
        cat_scroll = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.category_tree.yview)
        cat_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.category_tree.configure(yscrollcommand=cat_scroll.set)
        
        # Bind selection event
        self.category_tree.bind('<<TreeviewSelect>>', self.on_category_select)
    
    def create_airport_panel(self, parent):
        """Create the airport list panel."""
        frame = ttk.LabelFrame(parent, text="Airports", padding=5)
        parent.add(frame, weight=1)
        
        # Listbox for airports
        self.airport_listbox = tk.Listbox(frame, selectmode='browse')
        self.airport_listbox.pack(fill=tk.BOTH, expand=True)
        
        # Scrollbar for airport list
        airport_scroll = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.airport_listbox.yview)
        airport_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.airport_listbox.configure(yscrollcommand=airport_scroll.set)
        
        # Bind selection event
        self.airport_listbox.bind('<<ListboxSelect>>', self.on_airport_select)
    
    def create_details_panel(self, parent):
        """Create the record details panel."""
        frame = ttk.LabelFrame(parent, text="Record Details", padding=5)
        parent.add(frame, weight=2)
        
        # Notebook for different views
        notebook = ttk.Notebook(frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Records list tab
        records_frame = ttk.Frame(notebook)
        notebook.add(records_frame, text="Records")
        
        self.records_tree = ttk.Treeview(records_frame, columns=('Type', 'Line'), show='tree headings')
        self.records_tree.heading('#0', text='Record')
        self.records_tree.heading('Type', text='Type')
        self.records_tree.heading('Line', text='Line #')
        self.records_tree.pack(fill=tk.BOTH, expand=True)
        
        # Record details tab
        details_frame = ttk.Frame(notebook)
        notebook.add(details_frame, text="Details")
        
        self.details_text = scrolledtext.ScrolledText(details_frame, wrap=tk.WORD)
        self.details_text.pack(fill=tk.BOTH, expand=True)
        
        # Bind record selection
        self.records_tree.bind('<<TreeviewSelect>>', self.on_record_select)

    def open_file(self):
        """Open and parse an ARINC file."""
        filetypes = [
            ("All files", "*.*"),
            ("Text files", "*.txt"),
            ("ARINC files", "*.arinc")
        ]

        filename = filedialog.askopenfilename(
            title="Select ARINC 424 Database File",
            filetypes=filetypes
        )

        if filename:
            try:
                self.status_var.set("Parsing file...")
                self.root.update()

                self.parser.parse_file(filename)
                self.current_file = filename

                self.populate_category_tree()
                self.clear_airport_list()
                self.clear_details()

                record_count = len(self.parser.raw_records)
                category_count = len(self.parser.get_categories())

                self.status_var.set(f"Loaded {record_count} records in {category_count} categories from {os.path.basename(filename)}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to parse file:\n{str(e)}")
                self.status_var.set("Error loading file")

    def populate_category_tree(self):
        """Populate the category tree with parsed data."""
        # Clear existing items
        for item in self.category_tree.get_children():
            self.category_tree.delete(item)

        # Add categories
        for category in self.parser.get_categories():
            category_name = self.parser.get_category_name(category)
            airports = self.parser.get_airports_in_category(category)

            # Count total records in this category
            total_records = sum(len(self.parser.get_records_for_airport(category, airport))
                              for airport in airports)

            display_text = f"{category_name} ({len(airports)} airports, {total_records} records)"

            category_item = self.category_tree.insert('', 'end', text=display_text,
                                                    values=(category,), open=False)

            # Add airports as children
            for airport in airports:
                records = self.parser.get_records_for_airport(category, airport)
                airport_text = f"{airport} ({len(records)} records)"
                self.category_tree.insert(category_item, 'end', text=airport_text,
                                        values=(category, airport))

    def on_category_select(self, event):
        """Handle category tree selection."""
        selection = self.category_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.category_tree.item(item, 'values')

        if len(values) == 1:  # Category selected
            category = values[0]
            airports = self.parser.get_airports_in_category(category)
            self.populate_airport_list(airports)
            self.clear_details()
        elif len(values) == 2:  # Airport selected
            category, airport = values
            self.populate_airport_list([airport])
            self.populate_records_for_airport(category, airport)

    def populate_airport_list(self, airports):
        """Populate the airport list."""
        self.airport_listbox.delete(0, tk.END)
        for airport in airports:
            self.airport_listbox.insert(tk.END, airport)

    def clear_airport_list(self):
        """Clear the airport list."""
        self.airport_listbox.delete(0, tk.END)

    def on_airport_select(self, event):
        """Handle airport list selection."""
        selection = self.airport_listbox.curselection()
        if not selection:
            return

        airport = self.airport_listbox.get(selection[0])

        # Get currently selected category
        cat_selection = self.category_tree.selection()
        if cat_selection:
            values = self.category_tree.item(cat_selection[0], 'values')
            if values:
                category = values[0]
                self.populate_records_for_airport(category, airport)

    def populate_records_for_airport(self, category, airport):
        """Populate records for selected airport."""
        # Clear existing records
        for item in self.records_tree.get_children():
            self.records_tree.delete(item)

        records = self.parser.get_records_for_airport(category, airport)

        for i, record_data in enumerate(records):
            record = record_data['record']
            record_type = "Primary" if record.primary() else "Continuation"
            line_num = record_data['line_number']

            # Create a short description for the record
            description = f"Record {i+1}"
            if hasattr(record, 'definition') and hasattr(record.definition, 'name'):
                description = record.definition.name

            self.records_tree.insert('', 'end', text=description,
                                   values=(record_type, line_num),
                                   tags=(str(i),))

    def on_record_select(self, event):
        """Handle record selection to show details."""
        selection = self.records_tree.selection()
        if not selection:
            return

        item = selection[0]
        tags = self.records_tree.item(item, 'tags')
        if not tags:
            return

        record_index = int(tags[0])

        # Get current category and airport
        cat_selection = self.category_tree.selection()
        if not cat_selection:
            return

        values = self.category_tree.item(cat_selection[0], 'values')
        if len(values) < 2:
            return

        category, airport = values[0], values[1] if len(values) > 1 else None

        if airport:
            records = self.parser.get_records_for_airport(category, airport)
            if record_index < len(records):
                self.show_record_details(records[record_index])

    def show_record_details(self, record_data):
        """Show detailed information about a record."""
        record = record_data['record']

        # Clear previous content
        self.details_text.delete(1.0, tk.END)

        # Show record information
        details = []
        details.append(f"Line Number: {record_data['line_number']}")
        details.append(f"Category: {record_data['category']}")
        details.append(f"Airport: {record_data['airport']}")
        details.append(f"Record Type: {'Primary' if record.primary() else 'Continuation'}")
        details.append(f"Has Continuation: {'Yes' if record.hasCont() else 'No'}")
        details.append("")
        details.append("Raw Record:")
        details.append(record.raw)
        details.append("")
        details.append("Parsed Fields:")
        details.append("-" * 80)

        # Show parsed fields
        for field in record.fields:
            field_name = field.name.ljust(35)
            field_value = f"'{field.value}'".ljust(20)
            decoded_value = field.decode(record)
            details.append(f"{field_name} {field_value} {decoded_value}")

        self.details_text.insert(1.0, "\n".join(details))

    def clear_details(self):
        """Clear the details panel."""
        for item in self.records_tree.get_children():
            self.records_tree.delete(item)
        self.details_text.delete(1.0, tk.END)

    def refresh_view(self):
        """Refresh the current view."""
        if self.current_file:
            self.open_file_by_path(self.current_file)

    def open_file_by_path(self, filepath):
        """Open a file by its path."""
        try:
            self.parser.parse_file(filepath)
            self.populate_category_tree()
            self.clear_airport_list()
            self.clear_details()

            record_count = len(self.parser.raw_records)
            category_count = len(self.parser.get_categories())

            self.status_var.set(f"Loaded {record_count} records in {category_count} categories")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh file:\n{str(e)}")

    def expand_all(self):
        """Expand all items in the category tree."""
        def expand_item(item):
            self.category_tree.item(item, open=True)
            for child in self.category_tree.get_children(item):
                expand_item(child)

        for item in self.category_tree.get_children():
            expand_item(item)

    def collapse_all(self):
        """Collapse all items in the category tree."""
        def collapse_item(item):
            self.category_tree.item(item, open=False)
            for child in self.category_tree.get_children(item):
                collapse_item(child)

        for item in self.category_tree.get_children():
            collapse_item(item)

    def export_selected(self):
        """Export selected records to a file."""
        # Get current selection
        cat_selection = self.category_tree.selection()
        if not cat_selection:
            messagebox.showwarning("No Selection", "Please select a category or airport to export.")
            return

        values = self.category_tree.item(cat_selection[0], 'values')
        if not values:
            return

        # Determine what to export
        if len(values) == 1:  # Category selected
            category = values[0]
            airports = self.parser.get_airports_in_category(category)
            records_to_export = []
            for airport in airports:
                records_to_export.extend(self.parser.get_records_for_airport(category, airport))
            export_name = f"{self.parser.get_category_name(category)}"
        else:  # Airport selected
            category, airport = values
            records_to_export = self.parser.get_records_for_airport(category, airport)
            export_name = f"{airport} - {self.parser.get_category_name(category)}"

        if not records_to_export:
            messagebox.showwarning("No Data", "No records to export.")
            return

        self._export_records(records_to_export, export_name)

    def export_all(self):
        """Export all records to a file."""
        if not self.parser.raw_records:
            messagebox.showwarning("No Data", "No records to export.")
            return

        self._export_records(self.parser.raw_records, "All Records")

    def _export_records(self, records, default_name):
        """Export records to a file."""
        filetypes = [
            ("JSON files", "*.json"),
            ("Text files", "*.txt"),
            ("CSV files", "*.csv")
        ]

        filename = filedialog.asksaveasfilename(
            title="Export Records",
            defaultextension=".json",
            filetypes=filetypes,
            initialvalue=f"{default_name}.json"
        )

        if filename:
            try:
                if filename.endswith('.json'):
                    self._export_json(records, filename)
                elif filename.endswith('.csv'):
                    self._export_csv(records, filename)
                else:
                    self._export_text(records, filename)

                messagebox.showinfo("Export Complete", f"Exported {len(records)} records to {os.path.basename(filename)}")

            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export records:\n{str(e)}")

    def _export_json(self, records, filename):
        """Export records as JSON."""
        export_data = []
        for record_data in records:
            record = record_data['record']
            record_dict = {
                'line_number': record_data['line_number'],
                'category': record_data['category'],
                'airport': record_data['airport'],
                'raw': record_data['raw'],
                'fields': {}
            }

            for field in record.fields:
                record_dict['fields'][field.name] = {
                    'value': field.value,
                    'decoded': field.decode(record)
                }

            export_data.append(record_dict)

        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2)

    def _export_text(self, records, filename):
        """Export records as formatted text."""
        with open(filename, 'w') as f:
            for i, record_data in enumerate(records):
                record = record_data['record']
                f.write(f"Record {i+1}\n")
                f.write(f"Line: {record_data['line_number']}\n")
                f.write(f"Category: {record_data['category']}\n")
                f.write(f"Airport: {record_data['airport']}\n")
                f.write(f"Raw: {record_data['raw']}\n")
                f.write("\nFields:\n")

                for field in record.fields:
                    f.write(f"  {field.name}: '{field.value}' -> {field.decode(record)}\n")

                f.write("\n" + "="*80 + "\n\n")

    def _export_csv(self, records, filename):
        """Export records as CSV."""
        import csv

        with open(filename, 'w', newline='') as f:
            writer = csv.writer(f)

            # Write header
            if records:
                header = ['Line', 'Category', 'Airport', 'Raw']
                sample_record = records[0]['record']
                for field in sample_record.fields:
                    header.extend([f"{field.name}_Value", f"{field.name}_Decoded"])
                writer.writerow(header)

                # Write data
                for record_data in records:
                    record = record_data['record']
                    row = [
                        record_data['line_number'],
                        record_data['category'],
                        record_data['airport'],
                        record_data['raw']
                    ]

                    for field in record.fields:
                        row.extend([field.value, field.decode(record)])

                    writer.writerow(row)

    def show_about(self):
        """Show about dialog."""
        about_text = """ARINC 424 Database Parser

A GUI application for parsing and organizing ARINC 424 navigation database files.

Features:
• Parse ARINC 424 files
• Organize records by category and airport
• View detailed record information
• Export data in multiple formats
• Map visualization of coordinates

Built with Python and the arinc424 library.
        """
        messagebox.showinfo("About", about_text)

    def show_map(self):
        """Show interactive map with plotted coordinates."""
        if not MAPPING_AVAILABLE:
            messagebox.showerror("Map Error", "Folium library not available. Please install with: pip install folium")
            return

        if not self.parser.coordinates:
            messagebox.showwarning("No Data", "No coordinates available to display on map.")
            return

        # Get current selection for filtering
        coords_to_plot = self._get_selected_coordinates()

        if not coords_to_plot:
            coords_to_plot = self.parser.coordinates

        # Calculate bounds
        bounds = calculate_bounds(coords_to_plot)
        if not bounds:
            messagebox.showwarning("No Data", "No valid coordinates to display.")
            return

        # Create map
        center_lat = bounds['center_lat']
        center_lon = bounds['center_lon']

        # Create folium map
        m = folium.Map(location=[center_lat, center_lon], zoom_start=10)

        # Color mapping for different coordinate types
        color_map = {
            'Airport': 'red',
            'Runway': 'blue',
            'Waypoint': 'green',
            'Localizer': 'purple',
            'Glideslope': 'orange',
            'Marker': 'pink',
            'Navaid': 'darkgreen',
            'Azimuth': 'darkblue',
            'Elevation': 'darkred',
            'Other': 'gray'
        }

        # Add markers for each coordinate
        for coord in coords_to_plot:
            color = color_map.get(coord['type'], 'gray')

            popup_text = f"""
            <b>{coord['name']}</b><br>
            Type: {coord['type']}<br>
            Category: {coord['category']}<br>
            Airport: {coord['airport']}<br>
            Coordinates: {format_coordinate_display(coord['lat'], coord['lon'])}<br>
            Lat: {coord['lat']:.6f}<br>
            Lon: {coord['lon']:.6f}
            """

            folium.Marker(
                [coord['lat'], coord['lon']],
                popup=folium.Popup(popup_text, max_width=300),
                tooltip=coord['name'],
                icon=folium.Icon(color=color, icon='info-sign')
            ).add_to(m)

        # Add legend
        legend_html = '''
        <div style="position: fixed;
                    bottom: 50px; left: 50px; width: 150px; height: 200px;
                    background-color: white; border:2px solid grey; z-index:9999;
                    font-size:14px; padding: 10px">
        <p><b>Legend</b></p>
        '''

        for coord_type, color in color_map.items():
            if any(c['type'] == coord_type for c in coords_to_plot):
                legend_html += f'<p><i class="fa fa-map-marker" style="color:{color}"></i> {coord_type}</p>'

        legend_html += '</div>'
        m.get_root().html.add_child(folium.Element(legend_html))

        # Save to temporary file and open in browser
        import tempfile
        import webbrowser

        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            m.save(f.name)
            webbrowser.open('file://' + f.name)

        self.status_var.set(f"Map opened in browser with {len(coords_to_plot)} coordinates")

    def show_coordinates(self):
        """Show coordinate information in a new window."""
        coords_to_show = self._get_selected_coordinates()

        if not coords_to_show:
            coords_to_show = self.parser.coordinates

        if not coords_to_show:
            messagebox.showwarning("No Data", "No coordinates available to display.")
            return

        # Create new window
        coord_window = tk.Toplevel(self.root)
        coord_window.title("Coordinate Information")
        coord_window.geometry("800x600")

        # Create treeview for coordinates
        columns = ('Name', 'Type', 'Category', 'Airport', 'Latitude', 'Longitude', 'Formatted')
        coord_tree = ttk.Treeview(coord_window, columns=columns, show='headings')

        # Configure columns
        coord_tree.heading('Name', text='Name')
        coord_tree.heading('Type', text='Type')
        coord_tree.heading('Category', text='Category')
        coord_tree.heading('Airport', text='Airport')
        coord_tree.heading('Latitude', text='Latitude')
        coord_tree.heading('Longitude', text='Longitude')
        coord_tree.heading('Formatted', text='Formatted')

        # Set column widths
        coord_tree.column('Name', width=200)
        coord_tree.column('Type', width=80)
        coord_tree.column('Category', width=80)
        coord_tree.column('Airport', width=80)
        coord_tree.column('Latitude', width=100)
        coord_tree.column('Longitude', width=100)
        coord_tree.column('Formatted', width=150)

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(coord_window, orient=tk.VERTICAL, command=coord_tree.yview)
        h_scrollbar = ttk.Scrollbar(coord_window, orient=tk.HORIZONTAL, command=coord_tree.xview)
        coord_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack widgets
        coord_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Populate with coordinate data
        for coord in coords_to_show:
            formatted = format_coordinate_display(coord['lat'], coord['lon'])
            coord_tree.insert('', 'end', values=(
                coord['name'],
                coord['type'],
                coord['category'],
                coord['airport'],
                f"{coord['lat']:.6f}",
                f"{coord['lon']:.6f}",
                formatted
            ))

        # Add status label
        status_label = ttk.Label(coord_window, text=f"Showing {len(coords_to_show)} coordinates")
        status_label.pack(side=tk.BOTTOM, pady=5)

    def _get_selected_coordinates(self):
        """Get coordinates for currently selected category/airport."""
        # Get current selection
        cat_selection = self.category_tree.selection()
        if not cat_selection:
            return []

        values = self.category_tree.item(cat_selection[0], 'values')
        if not values:
            return []

        if len(values) == 1:  # Category selected
            category = values[0]
            return self.parser.get_coordinates(category=category)
        elif len(values) == 2:  # Airport selected
            category, airport = values
            return self.parser.get_coordinates(category=category, airport=airport)

        return []


def main():
    """Main entry point."""
    root = tk.Tk()
    app = ARINCGUIApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
