#!/usr/bin/env python3
"""
ARINC 424 Database GUI Parser

A GUI application for parsing and organizing ARINC 424 navigation database files.
Records are categorized by type and organized by airport for easy browsing.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import arinc424
import os
from collections import defaultdict
import json
import webbrowser
import tempfile
from coordinate_utils import extract_coordinates_from_record, calculate_bounds, format_coordinate_display

# Try to import mapping libraries
try:
    import folium
    MAPPING_AVAILABLE = True
except ImportError:
    MAPPING_AVAILABLE = False
    print("Warning: folium not available. Map features will be disabled.")

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("Warning: matplotlib not available. Plot features will be disabled.")


class ARINCParser:
    """Handles parsing ARINC files and organizing data by categories and airports."""
    
    def __init__(self):
        self.records = defaultdict(lambda: defaultdict(list))  # category -> airport -> records
        self.raw_records = []
        self.coordinates = []  # List of all extracted coordinates
        self.category_names = {
            'PA': 'Airport Reference Point',
            'PG': 'Runway',
            'PC': 'Terminal Waypoint', 
            'PD': 'SID/STAR/Approach',
            'PE': 'SID/STAR/Approach',
            'PF': 'SID/STAR/Approach',
            'PI': 'Localizer/ILS',
            'PL': 'MLS',
            'PM': 'Localizer Marker',
            'PN': 'NDB Navaid',
            'PS': 'MSA',
            'PT': 'GLS',
            'PV': 'Airport Communication',
            'D ': 'VHF Navaid',
            'DB': 'NDB Navaid',
            'EA': 'Enroute Waypoint',
            'EM': 'Airways Marker',
            'EP': 'Holding Pattern',
            'ER': 'Enroute Airways',
            'ET': 'Preferred Route',
            'EU': 'Enroute Airways Restriction',
            'EV': 'Enroute Communications',
            'HA': 'Heliport',
            'HC': 'Heliport Terminal Waypoint',
            'HD': 'Heliport SID/STAR/Approach',
            'HE': 'Heliport SID/STAR/Approach',
            'HF': 'Heliport SID/STAR/Approach',
            'HK': 'Heliport TAA',
            'HS': 'Heliport MSA',
            'HV': 'Heliport Communications',
            'AS': 'Grid MORA',
            'UC': 'Controlled Airspace',
            'UF': 'FIR/UIR',
            'UR': 'Restrictive Airspace'
        }
    
    def parse_file(self, filepath):
        """Parse an ARINC file and organize records by category and airport."""
        self.records.clear()
        self.raw_records.clear()
        self.coordinates.clear()

        try:
            with open(filepath, 'r') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('*') or line.startswith('#'):
                        continue

                    # Skip header lines (HDR01, HDR02, etc.)
                    if line.startswith('HDR'):
                        continue

                    record = arinc424.Record()
                    if record.read(line):
                        # Get category (section code)
                        category = self._get_category(record)

                        # Get airport identifier
                        airport = self._get_airport_identifier(record)

                        # Store the record
                        record_data = {
                            'line_number': line_num,
                            'raw': line,
                            'record': record,
                            'category': category,
                            'airport': airport
                        }

                        self.records[category][airport].append(record_data)
                        self.raw_records.append(record_data)

                        # Extract coordinates from this record
                        coords = extract_coordinates_from_record(record_data)
                        self.coordinates.extend(coords)

        except Exception as e:
            raise Exception(f"Error parsing file: {str(e)}")
    
    def _get_category(self, record):
        """Extract category from record identifier."""
        return record.ident
    
    def _get_airport_identifier(self, record):
        """Extract airport identifier from record."""
        try:
            # Try to find airport identifier in the record fields
            for field in record.fields:
                if 'Airport' in field.name and 'Identifier' in field.name:
                    return field.value.strip() if field.value else 'UNKNOWN'
                elif 'Heliport' in field.name and 'Identifier' in field.name:
                    return field.value.strip() if field.value else 'UNKNOWN'

            # Fallback: extract from raw record based on position
            if hasattr(record, 'raw') and record.raw and len(record.raw) >= 10:
                return record.raw[6:10].strip()

            return 'UNKNOWN'

        except (AttributeError, IndexError, TypeError) as e:
            print(f"Error extracting airport identifier: {e}")
            return 'ERROR'
    
    def get_categories(self):
        """Get all available categories."""
        return sorted(self.records.keys())
    
    def get_airports_in_category(self, category):
        """Get all airports in a specific category."""
        if category in self.records:
            return sorted(self.records[category].keys())
        return []
    
    def get_records_for_airport(self, category, airport):
        """Get all records for a specific airport in a category."""
        if category in self.records and airport in self.records[category]:
            return self.records[category][airport]
        return []
    
    def get_category_name(self, category):
        """Get human-readable name for category."""
        return self.category_names.get(category, f"Unknown ({category})")

    def get_coordinates(self, category=None, airport=None):
        """Get coordinates, optionally filtered by category and/or airport."""
        if not category and not airport:
            return self.coordinates

        filtered_coords = []
        for coord in self.coordinates:
            if category and coord['category'] != category:
                continue
            if airport and coord['airport'] != airport:
                continue
            filtered_coords.append(coord)

        return filtered_coords

    def get_coordinate_bounds(self, category=None, airport=None):
        """Get bounding box for coordinates."""
        coords = self.get_coordinates(category, airport)
        return calculate_bounds(coords) if coords else None


class ARINCGUIApp:
    """Main GUI application for ARINC database parsing."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("ARINC 424 Database Parser")
        self.root.geometry("1200x800")
        
        self.parser = ARINCParser()
        self.current_file = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface."""
        # Create menu bar
        self.create_menu()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create main content area with three panels
        self.create_main_panels()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_menu(self):
        """Create the menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open ARINC File...", command=self.open_file, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="Export Selected...", command=self.export_selected)
        file_menu.add_command(label="Export All...", command=self.export_all)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Refresh", command=self.refresh_view)
        view_menu.add_command(label="Expand All", command=self.expand_all)
        view_menu.add_command(label="Collapse All", command=self.collapse_all)
        view_menu.add_separator()
        if MAPPING_AVAILABLE:
            view_menu.add_command(label="Show Map", command=self.show_map)
            view_menu.add_command(label="Show Airport Map", command=self.show_airport_map)
        view_menu.add_command(label="Show Coordinates", command=self.show_coordinates)
        view_menu.add_command(label="Show Airport Coordinates", command=self.show_airport_coordinates)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        
        # Bind keyboard shortcuts
        self.root.bind('<Control-o>', lambda e: self.open_file())
    
    def create_toolbar(self):
        """Create the toolbar."""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        ttk.Button(toolbar, text="Open File", command=self.open_file).pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        ttk.Button(toolbar, text="Refresh", command=self.refresh_view).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Export", command=self.export_selected).pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        if MAPPING_AVAILABLE:
            ttk.Button(toolbar, text="Show Map", command=self.show_map).pack(side=tk.LEFT, padx=2)
            ttk.Button(toolbar, text="Airport Map", command=self.show_airport_map).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Coordinates", command=self.show_coordinates).pack(side=tk.LEFT, padx=2)
    
    def create_main_panels(self):
        """Create the main three-panel layout."""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create paned window for resizable panels
        paned = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # Left panel: Category tree
        self.create_category_panel(paned)
        
        # Middle panel: Airport list
        self.create_airport_panel(paned)
        
        # Right panel: Record details
        self.create_details_panel(paned)
    
    def create_category_panel(self, parent):
        """Create the category tree panel."""
        frame = ttk.LabelFrame(parent, text="Categories", padding=5)
        parent.add(frame, weight=1)
        
        # Tree view for categories
        self.category_tree = ttk.Treeview(frame, selectmode='browse')
        self.category_tree.pack(fill=tk.BOTH, expand=True)
        
        # Scrollbar for category tree
        cat_scroll = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.category_tree.yview)
        cat_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.category_tree.configure(yscrollcommand=cat_scroll.set)
        
        # Bind selection event
        self.category_tree.bind('<<TreeviewSelect>>', self.on_category_select)
    
    def create_airport_panel(self, parent):
        """Create the airport list panel."""
        frame = ttk.LabelFrame(parent, text="Airports", padding=5)
        parent.add(frame, weight=1)
        
        # Listbox for airports
        self.airport_listbox = tk.Listbox(frame, selectmode='browse')
        self.airport_listbox.pack(fill=tk.BOTH, expand=True)
        
        # Scrollbar for airport list
        airport_scroll = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.airport_listbox.yview)
        airport_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.airport_listbox.configure(yscrollcommand=airport_scroll.set)
        
        # Bind selection event
        self.airport_listbox.bind('<<ListboxSelect>>', self.on_airport_select)
    
    def create_details_panel(self, parent):
        """Create the record details panel."""
        frame = ttk.LabelFrame(parent, text="Record Details", padding=5)
        parent.add(frame, weight=2)
        
        # Notebook for different views
        notebook = ttk.Notebook(frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Records list tab
        records_frame = ttk.Frame(notebook)
        notebook.add(records_frame, text="Records")
        
        self.records_tree = ttk.Treeview(records_frame, columns=('Type', 'Line'), show='tree headings')
        self.records_tree.heading('#0', text='Record')
        self.records_tree.heading('Type', text='Type')
        self.records_tree.heading('Line', text='Line #')
        self.records_tree.pack(fill=tk.BOTH, expand=True)
        
        # Record details tab
        details_frame = ttk.Frame(notebook)
        notebook.add(details_frame, text="Details")
        
        self.details_text = scrolledtext.ScrolledText(details_frame, wrap=tk.WORD)
        self.details_text.pack(fill=tk.BOTH, expand=True)
        
        # Bind record selection
        self.records_tree.bind('<<TreeviewSelect>>', self.on_record_select)

    def open_file(self):
        """Open and parse an ARINC file."""
        filetypes = [
            ("All files", "*.*"),
            ("Text files", "*.txt"),
            ("ARINC files", "*.arinc")
        ]

        filename = filedialog.askopenfilename(
            title="Select ARINC 424 Database File",
            filetypes=filetypes
        )

        if filename:
            try:
                self.status_var.set("Parsing file...")
                self.root.update()

                self.parser.parse_file(filename)
                self.current_file = filename

                self.populate_category_tree()
                self.clear_airport_list()
                self.clear_details()

                record_count = len(self.parser.raw_records)
                category_count = len(self.parser.get_categories())

                self.status_var.set(f"Loaded {record_count} records in {category_count} categories from {os.path.basename(filename)}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to parse file:\n{str(e)}")
                self.status_var.set("Error loading file")

    def populate_category_tree(self):
        """Populate the category tree with parsed data."""
        # Clear existing items
        for item in self.category_tree.get_children():
            self.category_tree.delete(item)

        # Add categories
        for category in self.parser.get_categories():
            category_name = self.parser.get_category_name(category)
            airports = self.parser.get_airports_in_category(category)

            # Count total records in this category
            total_records = sum(len(self.parser.get_records_for_airport(category, airport))
                              for airport in airports)

            display_text = f"{category_name} ({len(airports)} airports, {total_records} records)"

            category_item = self.category_tree.insert('', 'end', text=display_text,
                                                    values=(category,), open=False)

            # Add airports as children
            for airport in airports:
                records = self.parser.get_records_for_airport(category, airport)
                airport_text = f"{airport} ({len(records)} records)"
                self.category_tree.insert(category_item, 'end', text=airport_text,
                                        values=(category, airport))

    def on_category_select(self, event):
        """Handle category tree selection."""
        selection = self.category_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.category_tree.item(item, 'values')

        if len(values) == 1:  # Category selected
            category = values[0]
            airports = self.parser.get_airports_in_category(category)
            self.populate_airport_list(airports)
            self.clear_details()
        elif len(values) == 2:  # Airport selected
            category, airport = values
            self.populate_airport_list([airport])
            self.populate_records_for_airport(category, airport)

    def populate_airport_list(self, airports):
        """Populate the airport list."""
        self.airport_listbox.delete(0, tk.END)
        for airport in airports:
            self.airport_listbox.insert(tk.END, airport)

    def clear_airport_list(self):
        """Clear the airport list."""
        self.airport_listbox.delete(0, tk.END)

    def on_airport_select(self, event):
        """Handle airport list selection."""
        selection = self.airport_listbox.curselection()
        if not selection:
            return

        airport = self.airport_listbox.get(selection[0])

        # Get currently selected category
        cat_selection = self.category_tree.selection()
        if cat_selection:
            values = self.category_tree.item(cat_selection[0], 'values')
            if values:
                category = values[0]
                self.populate_records_for_airport(category, airport)

    def populate_records_for_airport(self, category, airport):
        """Populate records for selected airport."""
        # Clear existing records
        for item in self.records_tree.get_children():
            self.records_tree.delete(item)

        records = self.parser.get_records_for_airport(category, airport)

        for i, record_data in enumerate(records):
            record = record_data['record']
            record_type = "Primary" if record.primary() else "Continuation"
            line_num = record_data['line_number']

            # Create a short description for the record
            description = f"Record {i+1}"
            if hasattr(record, 'definition') and hasattr(record.definition, 'name'):
                description = record.definition.name

            self.records_tree.insert('', 'end', text=description,
                                   values=(record_type, line_num),
                                   tags=(str(i),))

    def on_record_select(self, event):
        """Handle record selection to show details."""
        selection = self.records_tree.selection()
        if not selection:
            return

        item = selection[0]
        tags = self.records_tree.item(item, 'tags')
        if not tags:
            return

        record_index = int(tags[0])

        # Get current category and airport
        cat_selection = self.category_tree.selection()
        if not cat_selection:
            return

        values = self.category_tree.item(cat_selection[0], 'values')
        if len(values) < 2:
            return

        category, airport = values[0], values[1] if len(values) > 1 else None

        if airport:
            records = self.parser.get_records_for_airport(category, airport)
            if record_index < len(records):
                self.show_record_details(records[record_index])

    def show_record_details(self, record_data):
        """Show detailed information about a record."""
        record = record_data['record']

        # Clear previous content
        self.details_text.delete(1.0, tk.END)

        # Show record information
        details = []
        details.append(f"Line Number: {record_data['line_number']}")
        details.append(f"Category: {record_data['category']}")
        details.append(f"Airport: {record_data['airport']}")
        details.append(f"Record Type: {'Primary' if record.primary() else 'Continuation'}")
        details.append(f"Has Continuation: {'Yes' if record.hasCont() else 'No'}")
        details.append("")
        details.append("Raw Record:")
        details.append(record.raw)
        details.append("")
        details.append("Parsed Fields:")
        details.append("-" * 80)

        # Show parsed fields
        for field in record.fields:
            field_name = field.name.ljust(35)
            field_value = f"'{field.value}'".ljust(20)
            decoded_value = field.decode(record)
            details.append(f"{field_name} {field_value} {decoded_value}")

        self.details_text.insert(1.0, "\n".join(details))

    def clear_details(self):
        """Clear the details panel."""
        for item in self.records_tree.get_children():
            self.records_tree.delete(item)
        self.details_text.delete(1.0, tk.END)

    def refresh_view(self):
        """Refresh the current view."""
        if self.current_file:
            self.open_file_by_path(self.current_file)

    def open_file_by_path(self, filepath):
        """Open a file by its path."""
        try:
            self.parser.parse_file(filepath)
            self.populate_category_tree()
            self.clear_airport_list()
            self.clear_details()

            record_count = len(self.parser.raw_records)
            category_count = len(self.parser.get_categories())

            self.status_var.set(f"Loaded {record_count} records in {category_count} categories")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh file:\n{str(e)}")

    def expand_all(self):
        """Expand all items in the category tree."""
        def expand_item(item):
            self.category_tree.item(item, open=True)
            for child in self.category_tree.get_children(item):
                expand_item(child)

        for item in self.category_tree.get_children():
            expand_item(item)

    def collapse_all(self):
        """Collapse all items in the category tree."""
        def collapse_item(item):
            self.category_tree.item(item, open=False)
            for child in self.category_tree.get_children(item):
                collapse_item(child)

        for item in self.category_tree.get_children():
            collapse_item(item)

    def export_selected(self):
        """Export selected records to a file."""
        # Get current selection
        cat_selection = self.category_tree.selection()
        if not cat_selection:
            messagebox.showwarning("No Selection", "Please select a category or airport to export.")
            return

        values = self.category_tree.item(cat_selection[0], 'values')
        if not values:
            return

        # Determine what to export
        if len(values) == 1:  # Category selected
            category = values[0]
            airports = self.parser.get_airports_in_category(category)
            records_to_export = []
            for airport in airports:
                records_to_export.extend(self.parser.get_records_for_airport(category, airport))
            export_name = f"{self.parser.get_category_name(category)}"
        else:  # Airport selected
            category, airport = values
            records_to_export = self.parser.get_records_for_airport(category, airport)
            export_name = f"{airport} - {self.parser.get_category_name(category)}"

        if not records_to_export:
            messagebox.showwarning("No Data", "No records to export.")
            return

        self._export_records(records_to_export, export_name)

    def export_all(self):
        """Export all records to a file."""
        if not self.parser.raw_records:
            messagebox.showwarning("No Data", "No records to export.")
            return

        self._export_records(self.parser.raw_records, "All Records")

    def _export_records(self, records, default_name):
        """Export records to a file."""
        filetypes = [
            ("JSON files", "*.json"),
            ("Text files", "*.txt"),
            ("CSV files", "*.csv")
        ]

        filename = filedialog.asksaveasfilename(
            title="Export Records",
            defaultextension=".json",
            filetypes=filetypes,
            initialvalue=f"{default_name}.json"
        )

        if filename:
            try:
                if filename.endswith('.json'):
                    self._export_json(records, filename)
                elif filename.endswith('.csv'):
                    self._export_csv(records, filename)
                else:
                    self._export_text(records, filename)

                messagebox.showinfo("Export Complete", f"Exported {len(records)} records to {os.path.basename(filename)}")

            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export records:\n{str(e)}")

    def _export_json(self, records, filename):
        """Export records as JSON."""
        export_data = []
        for record_data in records:
            record = record_data['record']
            record_dict = {
                'line_number': record_data['line_number'],
                'category': record_data['category'],
                'airport': record_data['airport'],
                'raw': record_data['raw'],
                'fields': {}
            }

            for field in record.fields:
                record_dict['fields'][field.name] = {
                    'value': field.value,
                    'decoded': field.decode(record)
                }

            export_data.append(record_dict)

        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2)

    def _export_text(self, records, filename):
        """Export records as formatted text."""
        with open(filename, 'w') as f:
            for i, record_data in enumerate(records):
                record = record_data['record']
                f.write(f"Record {i+1}\n")
                f.write(f"Line: {record_data['line_number']}\n")
                f.write(f"Category: {record_data['category']}\n")
                f.write(f"Airport: {record_data['airport']}\n")
                f.write(f"Raw: {record_data['raw']}\n")
                f.write("\nFields:\n")

                for field in record.fields:
                    f.write(f"  {field.name}: '{field.value}' -> {field.decode(record)}\n")

                f.write("\n" + "="*80 + "\n\n")

    def _export_csv(self, records, filename):
        """Export records as CSV."""
        import csv

        with open(filename, 'w', newline='') as f:
            writer = csv.writer(f)

            # Write header
            if records:
                header = ['Line', 'Category', 'Airport', 'Raw']
                sample_record = records[0]['record']
                for field in sample_record.fields:
                    header.extend([f"{field.name}_Value", f"{field.name}_Decoded"])
                writer.writerow(header)

                # Write data
                for record_data in records:
                    record = record_data['record']
                    row = [
                        record_data['line_number'],
                        record_data['category'],
                        record_data['airport'],
                        record_data['raw']
                    ]

                    for field in record.fields:
                        row.extend([field.value, field.decode(record)])

                    writer.writerow(row)

    def show_about(self):
        """Show about dialog."""
        about_text = """ARINC 424 Database Parser

A GUI application for parsing and organizing ARINC 424 navigation database files.

Features:
• Parse ARINC 424 files
• Organize records by category and airport
• View detailed record information
• Export data in multiple formats
• Map visualization of coordinates

Built with Python and the arinc424 library.
        """
        messagebox.showinfo("About", about_text)

    def show_map(self):
        """Show interactive map with plotted coordinates."""
        if not MAPPING_AVAILABLE:
            messagebox.showerror("Map Error", "Folium library not available. Please install with: pip install folium")
            return

        if not self.parser.coordinates:
            messagebox.showwarning("No Data", "No coordinates available to display on map.")
            return

        # Get current selection for filtering
        coords_to_plot = self._get_selected_coordinates()

        if not coords_to_plot:
            # If no selection, warn about plotting all coordinates
            total_coords = len(self.parser.coordinates)
            if total_coords > 1000:
                result = messagebox.askyesno(
                    "Large Dataset",
                    f"You have {total_coords} coordinates. Plotting all of them may be slow.\n\n"
                    f"Recommended: Select a specific category or airport first.\n\n"
                    f"Do you want to plot all coordinates anyway?"
                )
                if not result:
                    return
            coords_to_plot = self.parser.coordinates

        # Limit coordinates for performance
        max_coords = 5000
        if len(coords_to_plot) > max_coords:
            coords_to_plot = coords_to_plot[:max_coords]
            messagebox.showinfo(
                "Coordinate Limit",
                f"Showing first {max_coords} coordinates for performance.\n"
                f"Select a specific category or airport to see fewer, more relevant coordinates."
            )

        # Calculate bounds
        bounds = calculate_bounds(coords_to_plot)
        if not bounds:
            messagebox.showwarning("No Data", "No valid coordinates to display.")
            return

        # Create map
        center_lat = bounds['center_lat']
        center_lon = bounds['center_lon']

        # Create folium map
        m = folium.Map(location=[center_lat, center_lon], zoom_start=10)

        # Color mapping for different coordinate types
        color_map = {
            'Airport': 'red',
            'Runway': 'blue',
            'Waypoint': 'green',
            'Localizer': 'purple',
            'Glideslope': 'orange',
            'Marker': 'pink',
            'Navaid': 'darkgreen',
            'Azimuth': 'darkblue',
            'Elevation': 'darkred',
            'Other': 'gray'
        }

        # Group waypoints by procedure/airway for connecting lines
        waypoint_groups = self._group_waypoints_for_lines(coords_to_plot)

        # Create feature groups for different route types (for layer control)
        # Main route category groups
        route_main_groups = {
            'SID_Main': folium.FeatureGroup(name='📤 SID Procedures', show=True),
            'STAR_Main': folium.FeatureGroup(name='📥 STAR Procedures', show=True),
            'Approach_Main': folium.FeatureGroup(name='🛬 Approaches', show=True),
            'Airway_Main': folium.FeatureGroup(name='🛣️ Airways', show=False),
            'Route_Main': folium.FeatureGroup(name='🗺️ Routes', show=False),
            'Other_Main': folium.FeatureGroup(name='❓ Other Connections', show=False)
        }

        # Individual procedure groups (will be populated dynamically)
        individual_route_groups = {}

        # Add connecting lines between related waypoints
        line_colors = {
            'SID': 'blue',
            'STAR': 'green',
            'Approach': 'red',
            'Airway': 'purple',
            'Route': 'orange',
            'Other': 'gray'
        }

        for group_name, waypoints in waypoint_groups.items():
            if len(waypoints) >= 2:
                # Determine line color and layer based on procedure type
                line_color = 'gray'
                layer_type = 'Other'

                if 'SID' in group_name.upper():
                    line_color = 'blue'
                    layer_type = 'SID'
                elif 'STAR' in group_name.upper():
                    line_color = 'green'
                    layer_type = 'STAR'
                elif any(word in group_name.upper() for word in ['APPR', 'APPROACH', 'ILS', 'RNAV']):
                    line_color = 'red'
                    layer_type = 'Approach'
                elif any(word in group_name.upper() for word in ['AIRWAY', 'AWY']):
                    line_color = 'purple'
                    layer_type = 'Airway'
                elif 'ROUTE' in group_name.upper():
                    line_color = 'orange'
                    layer_type = 'Route'

                # Create line connecting waypoints
                line_coords = [[wp['lat'], wp['lon']] for wp in waypoints]
                line = folium.PolyLine(
                    line_coords,
                    color=line_color,
                    weight=3,
                    opacity=0.7,
                    popup=f"Procedure: {group_name}"
                )

                # Add line to appropriate layer
                route_layers[layer_type].add_child(line)

        # Add all route layers to map
        for layer in route_layers.values():
            layer.add_to(m)

        # Create marker layers for different facility types
        marker_layers = {
            'Airports': folium.FeatureGroup(name='Airports', show=True),
            'Runways': folium.FeatureGroup(name='Runways', show=True),
            'Waypoints': folium.FeatureGroup(name='Waypoints', show=True),
            'Navigation Aids': folium.FeatureGroup(name='Navigation Aids', show=True),
            'Approaches': folium.FeatureGroup(name='Approach Facilities', show=False),
            'Other': folium.FeatureGroup(name='Other Facilities', show=False)
        }

        # Add markers for each coordinate
        for coord in coords_to_plot:
            color = color_map.get(coord['type'], 'gray')

            # Determine which layer this marker belongs to
            layer_name = 'Other'
            if coord['type'] == 'Airport':
                layer_name = 'Airports'
            elif coord['type'] == 'Runway':
                layer_name = 'Runways'
            elif coord['type'] == 'Waypoint':
                layer_name = 'Waypoints'
            elif coord['type'] in ['Navaid', 'VHF Navaid', 'NDB Navaid']:
                layer_name = 'Navigation Aids'
            elif coord['type'] in ['Localizer', 'Glideslope', 'Marker']:
                layer_name = 'Approaches'

            # Use different icons for waypoints vs other types
            if coord['type'] == 'Waypoint':
                icon = folium.Icon(color=color, icon='map-pin')
            else:
                icon = folium.Icon(color=color, icon='info-sign')

            popup_text = f"""
            <b>{coord['name']}</b><br>
            Type: {coord['type']}<br>
            Category: {coord['category']}<br>
            Airport: {coord['airport']}<br>
            Coordinates: {format_coordinate_display(coord['lat'], coord['lon'])}<br>
            Lat: {coord['lat']:.6f}<br>
            Lon: {coord['lon']:.6f}
            """

            marker = folium.Marker(
                [coord['lat'], coord['lon']],
                popup=folium.Popup(popup_text, max_width=300),
                tooltip=coord['name'],
                icon=icon
            )

            # Add marker to appropriate layer
            marker_layers[layer_name].add_child(marker)

        # Add all marker layers to map
        for layer in marker_layers.values():
            layer.add_to(m)

        # Add layer control widget
        folium.LayerControl(position='topright', collapsed=False).add_to(m)

        # Add enhanced legend with layer control instructions
        legend_html = '''
        <div style="position: fixed;
                    bottom: 50px; left: 50px; width: 250px; height: 350px;
                    background-color: white; border:2px solid grey; z-index:9999;
                    font-size:11px; padding: 10px">
        <p><b>Map Controls</b></p>
        <p style="color:blue;"><b>📋 Use Layer Control (top-right) to show/hide:</b></p>
        <p style="margin-left:10px;">• Route types (SID/STAR/Approaches/etc.)</p>
        <p style="margin-left:10px;">• Facility types (Airports/Runways/etc.)</p>

        <p><b>Markers:</b></p>
        '''

        for coord_type, color in color_map.items():
            if any(c['type'] == coord_type for c in coords_to_plot):
                legend_html += f'<p style="margin-left:10px;"><i class="fa fa-map-marker" style="color:{color}"></i> {coord_type}</p>'

        # Add line legend if waypoints are present
        if any(c['type'] == 'Waypoint' for c in coords_to_plot):
            legend_html += '''
            <p><b>Route Lines:</b></p>
            <p style="margin-left:10px;"><span style="color:blue">━━━</span> SID Procedures</p>
            <p style="margin-left:10px;"><span style="color:green">━━━</span> STAR Procedures</p>
            <p style="margin-left:10px;"><span style="color:red">━━━</span> Approaches</p>
            <p style="margin-left:10px;"><span style="color:purple">━━━</span> Airways</p>
            <p style="margin-left:10px;"><span style="color:orange">━━━</span> Routes</p>
            '''

        legend_html += '</div>'
        m.get_root().html.add_child(folium.Element(legend_html))

        # Save to temporary file and open in browser
        import tempfile
        import webbrowser

        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            m.save(f.name)
            webbrowser.open('file://' + f.name)

        self.status_var.set(f"Map opened in browser with {len(coords_to_plot)} coordinates")

    def show_coordinates(self):
        """Show coordinate information in a new window."""
        coords_to_show = self._get_selected_coordinates()

        if not coords_to_show:
            coords_to_show = self.parser.coordinates

        if not coords_to_show:
            messagebox.showwarning("No Data", "No coordinates available to display.")
            return

        # Create new window
        coord_window = tk.Toplevel(self.root)
        coord_window.title("Coordinate Information")
        coord_window.geometry("800x600")

        # Create treeview for coordinates
        columns = ('Name', 'Type', 'Category', 'Airport', 'Latitude', 'Longitude', 'Formatted')
        coord_tree = ttk.Treeview(coord_window, columns=columns, show='headings')

        # Configure columns
        coord_tree.heading('Name', text='Name')
        coord_tree.heading('Type', text='Type')
        coord_tree.heading('Category', text='Category')
        coord_tree.heading('Airport', text='Airport')
        coord_tree.heading('Latitude', text='Latitude')
        coord_tree.heading('Longitude', text='Longitude')
        coord_tree.heading('Formatted', text='Formatted')

        # Set column widths
        coord_tree.column('Name', width=200)
        coord_tree.column('Type', width=80)
        coord_tree.column('Category', width=80)
        coord_tree.column('Airport', width=80)
        coord_tree.column('Latitude', width=100)
        coord_tree.column('Longitude', width=100)
        coord_tree.column('Formatted', width=150)

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(coord_window, orient=tk.VERTICAL, command=coord_tree.yview)
        h_scrollbar = ttk.Scrollbar(coord_window, orient=tk.HORIZONTAL, command=coord_tree.xview)
        coord_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack widgets
        coord_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Populate with coordinate data
        for coord in coords_to_show:
            formatted = format_coordinate_display(coord['lat'], coord['lon'])
            coord_tree.insert('', 'end', values=(
                coord['name'],
                coord['type'],
                coord['category'],
                coord['airport'],
                f"{coord['lat']:.6f}",
                f"{coord['lon']:.6f}",
                formatted
            ))

        # Add status label
        status_label = ttk.Label(coord_window, text=f"Showing {len(coords_to_show)} coordinates")
        status_label.pack(side=tk.BOTTOM, pady=5)

    def _get_selected_coordinates(self):
        """Get coordinates for currently selected category/airport."""
        # Get current selection
        cat_selection = self.category_tree.selection()
        if not cat_selection:
            return []

        values = self.category_tree.item(cat_selection[0], 'values')
        if not values:
            return []

        if len(values) == 1:  # Category selected
            category = values[0]
            return self.parser.get_coordinates(category=category)
        elif len(values) == 2:  # Airport selected
            category, airport = values
            return self.parser.get_coordinates(category=category, airport=airport)

        return []

    def show_airport_map(self):
        """Show map with all coordinates for a specific airport."""
        if not MAPPING_AVAILABLE:
            messagebox.showerror("Map Error", "Folium library not available. Please install with: pip install folium")
            return

        # Get all unique airports
        airports = set()
        for coord in self.parser.coordinates:
            airports.add(coord['airport'])

        if not airports:
            messagebox.showwarning("No Data", "No airports with coordinates found.")
            return

        # Create airport selection dialog
        airport_window = tk.Toplevel(self.root)
        airport_window.title("Select Airport for Map")
        airport_window.geometry("400x500")

        tk.Label(airport_window, text="Select an airport to show all its coordinates on the map:",
                font=('Arial', 12)).pack(pady=10)

        # Create listbox with airports
        listbox_frame = tk.Frame(airport_window)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        airport_listbox = tk.Listbox(listbox_frame, selectmode='single')
        scrollbar = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=airport_listbox.yview)
        airport_listbox.configure(yscrollcommand=scrollbar.set)

        # Populate with airports and coordinate counts
        airport_coords = {}
        for airport in sorted(airports):
            coords = self.parser.get_coordinates(airport=airport)
            airport_coords[airport] = coords
            airport_listbox.insert(tk.END, f"{airport} ({len(coords)} coordinates)")

        airport_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Buttons
        button_frame = tk.Frame(airport_window)
        button_frame.pack(pady=10)

        def show_selected_airport():
            selection = airport_listbox.curselection()
            if not selection:
                messagebox.showwarning("No Selection", "Please select an airport.")
                return

            airport_text = airport_listbox.get(selection[0])
            airport_code = airport_text.split(' (')[0]  # Extract airport code

            coords = airport_coords[airport_code]
            if not coords:
                messagebox.showwarning("No Data", f"No coordinates found for {airport_code}")
                return

            self._create_map_for_coordinates(coords, f"Airport: {airport_code}")
            airport_window.destroy()

        def show_selected_info():
            selection = airport_listbox.curselection()
            if not selection:
                messagebox.showwarning("No Selection", "Please select an airport.")
                return

            airport_text = airport_listbox.get(selection[0])
            airport_code = airport_text.split(' (')[0]
            coords = airport_coords[airport_code]

            # Show coordinate breakdown
            coord_types = {}
            for coord in coords:
                coord_type = coord['type']
                coord_types[coord_type] = coord_types.get(coord_type, 0) + 1

            info_text = f"Airport: {airport_code}\n"
            info_text += f"Total Coordinates: {len(coords)}\n\n"
            info_text += "Breakdown by type:\n"
            for coord_type, count in sorted(coord_types.items()):
                info_text += f"  {coord_type}: {count}\n"

            messagebox.showinfo(f"Airport Info: {airport_code}", info_text)

        ttk.Button(button_frame, text="Show Map", command=show_selected_airport).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Show Info", command=show_selected_info).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=airport_window.destroy).pack(side=tk.LEFT, padx=5)

    def show_airport_coordinates(self):
        """Show coordinate table for a specific airport."""
        # Get all unique airports
        airports = set()
        for coord in self.parser.coordinates:
            airports.add(coord['airport'])

        if not airports:
            messagebox.showwarning("No Data", "No airports with coordinates found.")
            return

        # Create airport selection dialog
        airport_window = tk.Toplevel(self.root)
        airport_window.title("Select Airport for Coordinates")
        airport_window.geometry("400x500")

        tk.Label(airport_window, text="Select an airport to show all its coordinates:",
                font=('Arial', 12)).pack(pady=10)

        # Create listbox with airports
        listbox_frame = tk.Frame(airport_window)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        airport_listbox = tk.Listbox(listbox_frame, selectmode='single')
        scrollbar = tk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=airport_listbox.yview)
        airport_listbox.configure(yscrollcommand=scrollbar.set)

        # Populate with airports and coordinate counts
        airport_coords = {}
        for airport in sorted(airports):
            coords = self.parser.get_coordinates(airport=airport)
            airport_coords[airport] = coords
            airport_listbox.insert(tk.END, f"{airport} ({len(coords)} coordinates)")

        airport_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Buttons
        button_frame = tk.Frame(airport_window)
        button_frame.pack(pady=10)

        def show_selected_coordinates():
            selection = airport_listbox.curselection()
            if not selection:
                messagebox.showwarning("No Selection", "Please select an airport.")
                return

            airport_text = airport_listbox.get(selection[0])
            airport_code = airport_text.split(' (')[0]
            coords = airport_coords[airport_code]

            if not coords:
                messagebox.showwarning("No Data", f"No coordinates found for {airport_code}")
                return

            self._show_coordinates_window(coords, f"Coordinates for Airport: {airport_code}")
            airport_window.destroy()

        ttk.Button(button_frame, text="Show Coordinates", command=show_selected_coordinates).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=airport_window.destroy).pack(side=tk.LEFT, padx=5)

    def _create_map_for_coordinates(self, coords_to_plot, title="Map"):
        """Create and display a map for specific coordinates."""
        if not coords_to_plot:
            messagebox.showwarning("No Data", "No coordinates to display.")
            return

        self.status_var.set(f"Creating map with {len(coords_to_plot)} coordinates...")
        self.root.update()

        # Calculate bounds
        bounds = calculate_bounds(coords_to_plot)
        if not bounds:
            messagebox.showwarning("No Data", "No valid coordinates to display.")
            return

        # Create map
        center_lat = bounds['center_lat']
        center_lon = bounds['center_lon']

        # Create folium map
        import folium
        m = folium.Map(location=[center_lat, center_lon], zoom_start=12)

        # Color mapping for different coordinate types
        color_map = {
            'Airport': 'red',
            'Runway': 'blue',
            'Waypoint': 'green',
            'Localizer': 'purple',
            'Glideslope': 'orange',
            'Marker': 'pink',
            'Navaid': 'darkgreen',
            'Azimuth': 'darkblue',
            'Elevation': 'darkred',
            'Other': 'gray'
        }

        # Group waypoints by procedure/airway for connecting lines
        waypoint_groups = self._group_waypoints_for_lines(coords_to_plot)

        # Create feature groups for different route types (for layer control)
        # Main route category groups
        route_main_groups = {
            'SID_Main': folium.FeatureGroup(name='📤 SID Procedures', show=True),
            'STAR_Main': folium.FeatureGroup(name='📥 STAR Procedures', show=True),
            'Approach_Main': folium.FeatureGroup(name='🛬 Approaches', show=True),
            'Airway_Main': folium.FeatureGroup(name='🛣️ Airways', show=False),
            'Route_Main': folium.FeatureGroup(name='🗺️ Routes', show=False),
            'Other_Main': folium.FeatureGroup(name='❓ Other Connections', show=False)
        }

        # Individual procedure groups (will be populated dynamically)
        individual_route_groups = {}

        # Add connecting lines between related waypoints
        for group_name, waypoints in waypoint_groups.items():
            if len(waypoints) >= 2:
                # Determine line color and layer based on procedure type
                line_color = 'gray'
                main_layer_type = 'Other_Main'

                if 'SID' in group_name.upper():
                    line_color = 'blue'
                    main_layer_type = 'SID_Main'
                elif 'STAR' in group_name.upper():
                    line_color = 'green'
                    main_layer_type = 'STAR_Main'
                elif any(word in group_name.upper() for word in ['APPR', 'APPROACH', 'ILS', 'RNAV']):
                    line_color = 'red'
                    main_layer_type = 'Approach_Main'
                elif any(word in group_name.upper() for word in ['AIRWAY', 'AWY']):
                    line_color = 'purple'
                    main_layer_type = 'Airway_Main'
                elif 'ROUTE' in group_name.upper():
                    line_color = 'orange'
                    main_layer_type = 'Route_Main'

                # Create individual procedure group if it doesn't exist
                clean_group_name = group_name.replace('_', ' ').title()
                individual_key = f"{main_layer_type}_{group_name}"

                if individual_key not in individual_route_groups:
                    # Determine if this individual procedure should be shown by default
                    show_individual = main_layer_type in ['SID_Main', 'STAR_Main', 'Approach_Main']

                    individual_route_groups[individual_key] = folium.FeatureGroup(
                        name=f"  ├─ {clean_group_name}",
                        show=show_individual
                    )

                # Create line connecting waypoints
                line_coords = [[wp['lat'], wp['lon']] for wp in waypoints]
                line = folium.PolyLine(
                    line_coords,
                    color=line_color,
                    weight=3,
                    opacity=0.7,
                    popup=f"Procedure: {clean_group_name}<br>Waypoints: {len(waypoints)}"
                )

                # Add line to individual procedure group
                individual_route_groups[individual_key].add_child(line)

        # Add all main route groups to map first
        for layer in route_main_groups.values():
            layer.add_to(m)

        # Add all individual route groups to map
        for layer in individual_route_groups.values():
            layer.add_to(m)

        # Create marker layers for different facility types
        marker_layers = {
            'Airports': folium.FeatureGroup(name='Airports', show=True),
            'Runways': folium.FeatureGroup(name='Runways', show=True),
            'Waypoints': folium.FeatureGroup(name='Waypoints', show=True),
            'Navigation Aids': folium.FeatureGroup(name='Navigation Aids', show=True),
            'Approaches': folium.FeatureGroup(name='Approach Facilities', show=False),
            'Other': folium.FeatureGroup(name='Other Facilities', show=False)
        }

        # Add markers for each coordinate
        for coord in coords_to_plot:
            color = color_map.get(coord['type'], 'gray')

            # Determine which layer this marker belongs to
            layer_name = 'Other'
            if coord['type'] == 'Airport':
                layer_name = 'Airports'
            elif coord['type'] == 'Runway':
                layer_name = 'Runways'
            elif coord['type'] == 'Waypoint':
                layer_name = 'Waypoints'
            elif coord['type'] in ['Navaid', 'VHF Navaid', 'NDB Navaid']:
                layer_name = 'Navigation Aids'
            elif coord['type'] in ['Localizer', 'Glideslope', 'Marker']:
                layer_name = 'Approaches'

            # Use different icons for waypoints vs other types
            if coord['type'] == 'Waypoint':
                icon = folium.Icon(color=color, icon='map-pin')
            else:
                icon = folium.Icon(color=color, icon='info-sign')

            popup_text = f"""
            <b>{coord['name']}</b><br>
            Type: {coord['type']}<br>
            Category: {coord['category']}<br>
            Airport: {coord['airport']}<br>
            Coordinates: {format_coordinate_display(coord['lat'], coord['lon'])}<br>
            Lat: {coord['lat']:.6f}<br>
            Lon: {coord['lon']:.6f}
            """

            marker = folium.Marker(
                [coord['lat'], coord['lon']],
                popup=folium.Popup(popup_text, max_width=300),
                tooltip=coord['name'],
                icon=icon
            )

            # Add marker to appropriate layer
            marker_layers[layer_name].add_child(marker)

        # Add all marker layers to map
        for layer in marker_layers.values():
            layer.add_to(m)

        # Add layer control widget
        folium.LayerControl(position='topright', collapsed=False).add_to(m)

        # Add title
        title_html = f'<h3 align="center" style="font-size:16px"><b>{title}</b></h3>'
        m.get_root().html.add_child(folium.Element(title_html))

        # Save to temporary file and open in browser
        import tempfile
        import webbrowser

        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            m.save(f.name)
            webbrowser.open('file://' + f.name)

        self.status_var.set(f"Map opened with {len(coords_to_plot)} coordinates for {title}")

    def _show_coordinates_window(self, coords_to_show, title="Coordinates"):
        """Show coordinates in a dedicated window."""
        # Create new window
        coord_window = tk.Toplevel(self.root)
        coord_window.title(title)
        coord_window.geometry("1000x600")

        # Create treeview for coordinates
        columns = ('Name', 'Type', 'Category', 'Latitude', 'Longitude', 'Formatted')
        coord_tree = ttk.Treeview(coord_window, columns=columns, show='headings')

        # Configure columns
        coord_tree.heading('Name', text='Name')
        coord_tree.heading('Type', text='Type')
        coord_tree.heading('Category', text='Category')
        coord_tree.heading('Latitude', text='Latitude')
        coord_tree.heading('Longitude', text='Longitude')
        coord_tree.heading('Formatted', text='Formatted')

        # Set column widths
        coord_tree.column('Name', width=250)
        coord_tree.column('Type', width=100)
        coord_tree.column('Category', width=80)
        coord_tree.column('Latitude', width=120)
        coord_tree.column('Longitude', width=120)
        coord_tree.column('Formatted', width=200)

        # Add scrollbars
        tree_frame = tk.Frame(coord_window)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=coord_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=coord_tree.xview)
        coord_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack widgets
        coord_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Populate with coordinate data
        for coord in coords_to_show:
            formatted = format_coordinate_display(coord['lat'], coord['lon'])
            coord_tree.insert('', 'end', values=(
                coord['name'],
                coord['type'],
                coord['category'],
                f"{coord['lat']:.6f}",
                f"{coord['lon']:.6f}",
                formatted
            ))

        # Add status and buttons
        bottom_frame = tk.Frame(coord_window)
        bottom_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)

        status_label = ttk.Label(bottom_frame, text=f"Showing {len(coords_to_show)} coordinates")
        status_label.pack(side=tk.LEFT)

        # Add map button if mapping is available
        if MAPPING_AVAILABLE:
            def show_map_for_these():
                self._create_map_for_coordinates(coords_to_show, title)

            ttk.Button(bottom_frame, text="Show on Map", command=show_map_for_these).pack(side=tk.RIGHT, padx=5)

    def _group_waypoints_for_lines(self, coordinates):
        """Group waypoints that should be connected with lines."""
        groups = {}

        # Group by procedure/route identifiers
        for coord in coordinates:
            if coord['type'] != 'Waypoint':
                continue

            # Try to extract procedure information from the record
            record_data = None
            for raw_record in self.parser.raw_records:
                if (raw_record.get('category') == coord['category'] and
                    raw_record.get('airport') == coord['airport']):

                    # Look for procedure identifiers in the record fields
                    record = raw_record['record']
                    procedure_id = self._extract_procedure_identifier(record, coord)

                    if procedure_id:
                        group_key = f"{coord['airport']}_{procedure_id}"
                        if group_key not in groups:
                            groups[group_key] = []
                        groups[group_key].append(coord)
                        break

            # If no specific procedure found, group by airport and category
            if not any(coord in group for group in groups.values()):
                fallback_key = f"{coord['airport']}_{coord['category']}_waypoints"
                if fallback_key not in groups:
                    groups[fallback_key] = []
                groups[fallback_key].append(coord)

        # Sort waypoints within each group by sequence number if available
        for group_name, waypoints in groups.items():
            groups[group_name] = self._sort_waypoints_by_sequence(waypoints)

        # Only return groups with multiple waypoints
        return {k: v for k, v in groups.items() if len(v) >= 2}

    def _extract_procedure_identifier(self, record, coord):
        """Extract procedure identifier from record fields."""
        try:
            # Look for common procedure identifier fields
            procedure_fields = [
                'SID Identifier', 'STAR Identifier', 'Approach Identifier',
                'Route Identifier', 'Airway Identifier', 'Procedure Identifier',
                'Transition Identifier', 'Runway Transition'
            ]

            for field in record.fields:
                if any(proc_field in field.name for proc_field in procedure_fields):
                    if field.value and field.value.strip():
                        return field.value.strip()

                # Also check for route/airway in waypoint names
                if 'Route' in field.name or 'Airway' in field.name:
                    if field.value and field.value.strip():
                        return field.value.strip()

            return None

        except Exception:
            return None

    def _sort_waypoints_by_sequence(self, waypoints):
        """Sort waypoints by sequence number or other logical order."""
        try:
            # Try to find sequence numbers in the waypoint data
            waypoints_with_seq = []

            for wp in waypoints:
                seq_num = self._extract_sequence_number(wp)
                waypoints_with_seq.append((seq_num if seq_num is not None else 999, wp))

            # Sort by sequence number
            waypoints_with_seq.sort(key=lambda x: x[0])

            return [wp for seq, wp in waypoints_with_seq]

        except Exception:
            # Fallback: sort by waypoint name
            return sorted(waypoints, key=lambda x: x.get('name', ''))

    def _extract_sequence_number(self, waypoint):
        """Extract sequence number from waypoint data."""
        try:
            # Look for sequence indicators in the name or other fields
            name = waypoint.get('name', '')

            # Check for common sequence patterns
            import re

            # Pattern like "WAYPOINT_01", "WP001", etc.
            seq_match = re.search(r'(\d+)$', name)
            if seq_match:
                return int(seq_match.group(1))

            # Pattern like "01_WAYPOINT", "001WP", etc.
            seq_match = re.search(r'^(\d+)', name)
            if seq_match:
                return int(seq_match.group(1))

            return None

        except Exception:
            return None


def main():
    """Main entry point."""
    root = tk.Tk()
    app = ARINCGUIApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
