# ARINC 424 Database GUI Parser

A graphical user interface application for parsing and organizing ARINC 424 navigation database files. This application provides an intuitive way to browse aviation navigation data by categories and airports.

## Features

- **File Parsing**: Load and parse ARINC 424 database files
- **Category Organization**: Records are automatically sorted into categories (Airport, Runway, Navaid, etc.)
- **Airport Grouping**: Within each category, records are grouped by airport identifier
- **Detailed View**: View parsed fields and decoded values for each record
- **Export Functionality**: Export data in JSON, CSV, or text formats
- **Tree Navigation**: Hierarchical tree view for easy browsing
- **Search and Filter**: Navigate through categories and airports efficiently
- **🗺️ Interactive Maps**: Plot coordinates on interactive maps using Folium
- **📍 Coordinate Display**: View all extracted coordinates in a detailed table
- **🎯 Geographic Visualization**: Color-coded markers for different facility types

## Installation

1. **Install the ARINC424 library** (if not already installed):
   ```bash
   cd /path/to/arinc424-0.2.1
   python3 -m pip install -e .
   ```

2. **Install mapping dependencies** (for map features):
   ```bash
   python3 -m pip install folium matplotlib --user
   ```

3. **Run the GUI application**:
   ```bash
   python3 arinc_gui.py
   # OR use the launcher script
   ./run_gui.sh
   ```

## Usage

### Opening Files
1. Click "File" → "Open ARINC File..." or use Ctrl+O
2. Select your ARINC 424 database file
3. The application will parse the file and organize records automatically

### Navigating Data
1. **Categories Panel** (left): Shows record types (PA=Airport, PG=Runway, etc.)
2. **Airports Panel** (middle): Lists airports within the selected category
3. **Details Panel** (right): Shows individual records and their parsed fields

### Viewing Records
- Click on a category to see all airports in that category
- Click on an airport to see all records for that airport
- Select a record to view its detailed field information

### Exporting Data
- **Export Selected**: Export records from the currently selected category or airport
- **Export All**: Export all parsed records
- Supported formats: JSON, CSV, Text

### Map Visualization
- **Show Map**: Click "View" → "Show Map" or use the toolbar button
- **Interactive Maps**: Zoom, pan, and click markers for details
- **Color-Coded Markers**: Different colors for airports, runways, waypoints, etc.
- **Coordinate Display**: View all coordinates in a sortable table

### Map Features
- **Automatic Bounds**: Map automatically centers on your data
- **Popup Information**: Click markers to see detailed coordinate information
- **Legend**: Visual legend showing marker types and colors
- **Browser Integration**: Maps open in your default web browser

## Record Categories

The application organizes records into the following categories:

| Code | Description |
|------|-------------|
| PA   | Airport Reference Point |
| PG   | Runway |
| PC   | Terminal Waypoint |
| PD/PE/PF | SID/STAR/Approach Procedures |
| PI   | Localizer/ILS |
| PL   | MLS (Microwave Landing System) |
| PM   | Localizer Marker |
| PN   | NDB Navaid |
| PS   | MSA (Minimum Safe Altitude) |
| PT   | GLS (GNSS Landing System) |
| PV   | Airport Communication |
| D    | VHF Navaid |
| DB   | NDB Navaid |
| EA   | Enroute Waypoint |
| EM   | Airways Marker |
| EP   | Holding Pattern |
| ER   | Enroute Airways |
| ET   | Preferred Route |
| EU   | Enroute Airways Restriction |
| EV   | Enroute Communications |
| HA   | Heliport |
| HC   | Heliport Terminal Waypoint |
| HD/HE/HF | Heliport SID/STAR/Approach |
| HK   | Heliport TAA |
| HS   | Heliport MSA |
| HV   | Heliport Communications |
| AS   | Grid MORA |
| UC   | Controlled Airspace |
| UF   | FIR/UIR |
| UR   | Restrictive Airspace |

## Sample Data

The application includes sample ARINC 424 data files in the `data/ARINC-424-18/` directory:

- `airport` - Airport reference points
- `runway` - Runway information
- `navaid_vhf` - VHF navigation aids
- `terminal_waypoint` - Terminal waypoints
- And many more...

## Testing

Run the test script to verify the application works correctly:

```bash
python3 test_gui.py
```

This will test the parser with sample data and verify that records are properly categorized and organized.

## Requirements

- Python 3.10 or higher
- tkinter (usually included with Python)
- arinc424 library
- prettytable
- termcolor

### Optional (for mapping features):
- folium (for interactive maps)
- matplotlib (for plotting)

## File Structure

```
arinc424-0.2.1/
├── arinc_gui.py          # Main GUI application
├── arinc_gui_backup.py   # Backup of original GUI
├── coordinate_utils.py   # Coordinate parsing utilities
├── test_gui.py           # Test script
├── test_map.py           # Map functionality test
├── demo.py               # Demo script
├── run_gui.sh            # Launcher script
├── README_GUI.md         # This file
├── data/                 # Sample ARINC data
│   └── ARINC-424-18/
├── src/arinc424/         # ARINC424 library source
└── ...
```

## Troubleshooting

### Common Issues

1. **ModuleNotFoundError: No module named 'arinc424'**
   - Install the library: `python3 -m pip install -e .`

2. **File parsing errors**
   - Ensure the file is a valid ARINC 424 format
   - Check that lines are 132 characters long
   - Verify the file contains valid record types

3. **GUI not displaying**
   - Ensure tkinter is installed: `python3 -m tkinter`
   - Try running on a system with GUI support

### Getting Help

- Check the original ARINC424 library documentation
- Review the sample data files for format examples
- Use the test script to verify functionality

## License

This GUI application is provided as-is for use with the ARINC424 library. Please refer to the main project license for terms and conditions.
